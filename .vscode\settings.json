{"cmake.buildDirectory": "${workspaceFolder}/cmake_build", "cmake.preferredGenerators": ["Unix Makefiles", "Ninja"], "cmake.configureSettings": {"TOOLCHAIN_PATH": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32", "CMAKE_C_STANDARD_LIBRARIES": "", "CMAKE_CXX_STANDARD_LIBRARIES": "", "CMAKE_C_FLAGS_DEBUG": "", "CMAKE_C_FLAGS_RELEASE": "", "CMAKE_C_COMPILER_FORCED": 1, "CMAKE_CXX_COMPILER_FORCED": 1}, "cmake.environment": {"PATH": "C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\tc32\\bin;C:\\Users\\<USER>\\.Telink_Tools\\tc32_130_Windows\\bin;${env:PATH}"}, "cmake.parallelJobs": 24, "cmake.sourceDirectory": "${workspaceFolder}", "tlk.workToolchain": "TC32-GCC Toolchain", "tlk.workCProjectPath": "d:\\Telink_Project\\BMS_Base\\.cproject", "tlk.workProjectName": "BMS_Base", "files.associations": {"type_traits": "c", "tlkapi_debug.h": "c", "limits": "c", "user_app_main.h": "c", "register.h": "c", "array": "c", "string": "c", "string_view": "c", "app_main.h": "c", "drivers.h": "c", "config.h": "c", "tl_common.h": "c", "types.h": "c", "ble.h": "c", "bit.h": "c", "bms_data_manager_oop.h": "c", "app_att.h": "c", "app.h": "c", "app_usart.h": "c", "bms_data.h": "c"}}