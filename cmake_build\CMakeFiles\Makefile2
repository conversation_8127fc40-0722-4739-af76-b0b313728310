# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\Telink_Project\BMS_Base

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\Telink_Project\BMS_Base\cmake_build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/825x_ble_sample.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/825x_ble_sample.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/825x_ble_sample.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/825x_ble_sample.dir

# All Build rule for target.
CMakeFiles/825x_ble_sample.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\Telink_Project\BMS_Base\cmake_build\CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84 "Built target 825x_ble_sample"
.PHONY : CMakeFiles/825x_ble_sample.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/825x_ble_sample.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\Telink_Project\BMS_Base\cmake_build\CMakeFiles 84
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/825x_ble_sample.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\Telink_Project\BMS_Base\cmake_build\CMakeFiles 0
.PHONY : CMakeFiles/825x_ble_sample.dir/rule

# Convenience name for target.
825x_ble_sample: CMakeFiles/825x_ble_sample.dir/rule
.PHONY : 825x_ble_sample

# codegen rule for target.
CMakeFiles/825x_ble_sample.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=D:\Telink_Project\BMS_Base\cmake_build\CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84 "Finished codegen for target 825x_ble_sample"
.PHONY : CMakeFiles/825x_ble_sample.dir/codegen

# clean rule for target.
CMakeFiles/825x_ble_sample.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/clean
.PHONY : CMakeFiles/825x_ble_sample.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

