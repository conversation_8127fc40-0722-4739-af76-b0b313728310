/**
 * @file sh36760_example.c
 * @brief SH36760芯片模块使用示例
 * @version 1.0
 * @date 2025
 *
 * 本文件展示了如何使用SH36760芯片模块的函数指针接口：
 * - 基本的芯片操作调用
 * - 管理器功能使用
 * - 完整的工作流程示例
 */

#include "sh36760_chip.h"
#include "../../user_app_main.h"

/**
 * @brief SH36760芯片模块基本使用示例
 * @param device 设备实例指针
 * @param queue 队列指针
 * @note 展示基本的芯片操作调用方法
 */
void sh36760_basic_usage_example(SH367601B_Device* device, Queue* queue)
{
    printf("=== SH36760 Basic Usage Example ===\n");
    
    /* 1. 获取芯片操作接口 */
    SH36760_ChipInterface* chip_interface = sh36760_get_chip_interface();
    if (chip_interface == NULL) {
        printf("Error: Failed to get chip interface\n");
        return;
    }
    
    /* 2. 设置芯片数量 */
    printf("Setting chip count to 4...\n");
    int result = chip_interface->set_chip_count(device, 4);
    if (result == 0) {
        printf("Chip count set successfully\n");
    }
    
    /* 3. 获取当前芯片信息 */
    unsigned char chip_count = chip_interface->get_chip_count(device);
    unsigned char current_index = chip_interface->get_current_chip_index(device);
    printf("Current chip count: %d, Current chip index: %d\n", chip_count, current_index);
    
    /* 4. 切换到不同的芯片 */
    for (unsigned char i = 0; i < chip_count; i++) {
        printf("Switching to chip %d...\n", i);
        result = chip_interface->switch_to_chip(device, i);
        if (result == 0) {
            printf("Successfully switched to chip %d\n", i);
        }
    }
    
    /* 5. 写入ROM数据到当前芯片 */
    printf("Writing ROM data to current chip...\n");
    result = chip_interface->write_rom_for_chip(device, queue);
    if (result == 0) {
        printf("ROM data write initiated successfully\n");
    }
    
    /* 6. 检查写ROM流程状态 */
    unsigned char is_writing = chip_interface->is_write_rom_in_progress(device);
    printf("Write ROM in progress: %s\n", is_writing ? "Yes" : "No");
    
    printf("=== Basic Usage Example Completed ===\n\n");
}

/**
 * @brief SH36760芯片模块管理器使用示例
 * @note 展示如何使用芯片管理器的功能
 */
void sh36760_manager_usage_example(void)
{
    printf("=== SH36760 Manager Usage Example ===\n");
    
    /* 1. 获取芯片管理器 */
    SH36760_ChipManager* manager = sh36760_get_chip_manager();
    if (manager == NULL) {
        printf("Error: Failed to get chip manager\n");
        return;
    }
    
    /* 2. 初始化模块 */
    printf("Initializing SH36760 chip module...\n");
    int result = manager->methods.init();
    if (result == 0) {
        printf("Module initialized successfully\n");
    }
    
    /* 3. 获取模块信息 */
    printf("Getting module information...\n");
    manager->methods.get_info();
    
    /* 4. 显示模块状态 */
    printf("Module Status:\n");
    printf("  Name: %s\n", manager->info.module_name);
    printf("  Version: %s\n", manager->info.version);
    printf("  Initialized: %s\n", manager->info.is_initialized ? "Yes" : "No");
    
    /* 5. 重置模块 */
    printf("Resetting module...\n");
    manager->methods.reset();
    
    printf("=== Manager Usage Example Completed ===\n\n");
}

/**
 * @brief SH36760芯片模块批量写ROM示例
 * @param device 设备实例指针
 * @param queue 队列指针
 * @note 展示批量写ROM操作的完整流程
 */
void sh36760_batch_write_example(SH367601B_Device* device, Queue* queue)
{
    printf("=== SH36760 Batch Write ROM Example ===\n");
    
    /* 1. 获取芯片操作接口 */
    SH36760_ChipInterface* chip_interface = sh36760_get_chip_interface();
    if (chip_interface == NULL) {
        printf("Error: Failed to get chip interface\n");
        return;
    }
    
    /* 2. 设置芯片数量 */
    printf("Setting up 3 chips for batch write...\n");
    chip_interface->set_chip_count(device, 3);
    
    /* 3. 启动批量写ROM流程 */
    printf("Starting batch ROM write process...\n");
    int result = chip_interface->start_batch_write_rom(device, queue);
    if (result == 0) {
        printf("Batch ROM write process started successfully\n");
    }
    
    /* 4. 监控写入流程状态 */
    printf("Monitoring write process status...\n");
    unsigned char is_writing = chip_interface->is_write_rom_in_progress(device);
    printf("Write ROM in progress: %s\n", is_writing ? "Yes" : "No");
    
    /* 5. 显示当前处理的芯片 */
    unsigned char current_chip = chip_interface->get_current_chip_index(device);
    printf("Currently processing chip: %d\n", current_chip);
    
    printf("=== Batch Write ROM Example Completed ===\n\n");
}

/**
 * @brief SH36760芯片模块完整工作流程示例
 * @param device 设备实例指针
 * @param queue 队列指针
 * @note 展示一个完整的芯片操作工作流程
 */
void sh36760_complete_workflow_example(SH367601B_Device* device, Queue* queue)
{
    printf("=== SH36760 Complete Workflow Example ===\n");
    
    /* 步骤1: 初始化模块 */
    printf("Step 1: Initialize SH36760 module\n");
    int result = sh36760_chip_module_init();
    if (result != 0) {
        printf("Error: Module initialization failed\n");
        return;
    }
    
    /* 步骤2: 获取接口和管理器 */
    printf("Step 2: Get interface and manager\n");
    SH36760_ChipInterface* chip_interface = sh36760_get_chip_interface();
    SH36760_ChipManager* manager = sh36760_get_chip_manager();
    
    /* 步骤3: 显示模块信息 */
    printf("Step 3: Display module information\n");
    manager->methods.get_info();
    
    /* 步骤4: 配置芯片参数 */
    printf("Step 4: Configure chip parameters\n");
    chip_interface->set_chip_count(device, 2);
    
    /* 步骤5: 执行芯片操作 */
    printf("Step 5: Execute chip operations\n");
    
    /* 切换到芯片0并写入数据 */
    chip_interface->switch_to_chip(device, 0);
    chip_interface->write_rom_for_chip(device, queue);
    
    /* 切换到芯片1并写入数据 */
    chip_interface->switch_to_chip(device, 1);
    chip_interface->write_rom_for_chip(device, queue);
    
    /* 步骤6: 执行批量操作 */
    printf("Step 6: Execute batch operations\n");
    chip_interface->start_batch_write_rom(device, queue);
    
    /* 步骤7: 状态检查 */
    printf("Step 7: Status check\n");
    printf("Chip count: %d\n", chip_interface->get_chip_count(device));
    printf("Current chip: %d\n", chip_interface->get_current_chip_index(device));
    printf("Write in progress: %s\n", 
           chip_interface->is_write_rom_in_progress(device) ? "Yes" : "No");
    
    printf("=== Complete Workflow Example Completed ===\n\n");
}

/**
 * @brief SH36760芯片模块所有示例的主函数
 * @param device 设备实例指针
 * @param queue 队列指针
 * @note 运行所有示例函数
 */
void sh36760_run_all_examples(SH367601B_Device* device, Queue* queue)
{
    printf("========================================\n");
    printf("SH36760 Chip Module Examples\n");
    printf("========================================\n\n");
    
    /* 运行所有示例 */
    sh36760_manager_usage_example();
    sh36760_basic_usage_example(device, queue);
    sh36760_batch_write_example(device, queue);
    sh36760_complete_workflow_example(device, queue);
    
    printf("========================================\n");
    printf("All SH36760 Examples Completed\n");
    printf("========================================\n");
}
