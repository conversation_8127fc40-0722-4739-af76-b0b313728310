#include "sh36760_chip.h"
#include "sh367601xb_chip.h"
#include "../../user_app_main.h"  /* 引入队列定义 */

/**
 * @file sh36760_chip.c
 * @brief SH36760芯片模块实现 - 基于函数指针的芯片操作接口
 * @version 1.0
 * @date 2025
 *
 * 本文件实现了SH36760芯片的统一操作接口，通过函数指针封装：
 * - 复用sh367601xb_chip.c中的核心功能
 * - 提供统一的接口调用方式
 * - 支持扩展功能和模块管理
 */

/* ==========================================扩展功能函数实现========================================== */

/**
 * @brief 获取当前芯片索引
 * @param device 设备实例指针
 * @return 当前芯片索引
 */
unsigned char sh36760_get_current_chip_index(SH367601B_Device* device)
{
    if (device == NULL) return 0;
    return device->chip.data.current_chip_index;
}

/**
 * @brief 获取芯片数量
 * @param device 设备实例指针
 * @return 芯片数量
 */
unsigned char sh36760_get_chip_count(SH367601B_Device* device)
{
    if (device == NULL) return 0;
    return device->chip.data.chip_count;
}

/**
 * @brief 检查写ROM流程状态
 * @param device 设备实例指针
 * @return 1=写ROM流程进行中，0=空闲状态
 */
unsigned char sh36760_is_write_rom_in_progress(SH367601B_Device* device)
{
    if (device == NULL) return 0;
    return device->chip.data.write_rom_in_progress;
}

/* ==========================================SH36760芯片接口实现========================================== */

/**
 * @brief SH36760芯片接口实例（静态全局变量）
 * @note 包含所有芯片操作函数的指针，实现统一的接口调用
 */
static SH36760_ChipInterface sh36760_chip_interface = {
    .switch_to_chip = sh367601b_switch_to_chip,
    .write_rom_for_chip = sh367601b_write_rom_for_chip,
    .set_chip_count = sh367601b_set_chip_count,
    .start_batch_write_rom = sh367601b_start_batch_write_rom,
    .get_current_chip_index = sh36760_get_current_chip_index,
    .get_chip_count = sh36760_get_chip_count,
    .is_write_rom_in_progress = sh36760_is_write_rom_in_progress
};

/**
 * @brief 获取SH36760芯片操作接口
 * @return SH36760芯片接口结构体指针
 * @note 返回包含所有芯片操作函数指针的接口结构体
 */
SH36760_ChipInterface* sh36760_get_chip_interface(void)
{
    return &sh36760_chip_interface;
}

/* ==========================================SH36760芯片管理器实现========================================== */

/**
 * @brief 初始化SH36760芯片模块
 * @return 0=成功，-1=失败
 */
static int sh36760_manager_init(void)
{
    printf("SH36760: Chip module initialized\n");
    return 0;
}

/**
 * @brief 获取SH36760模块信息
 */
static void sh36760_manager_get_info(void)
{
    printf("=== SH36760 Chip Module Info ===\n");
    printf("Module Name: SH36760 Chip Interface\n");
    printf("Version: 1.0\n");
    printf("Description: Function pointer based chip operation interface\n");
    printf("Features:\n");
    printf("  - Chip switching\n");
    printf("  - ROM data writing\n");
    printf("  - Chip count management\n");
    printf("  - Batch write operations\n");
    printf("================================\n");
}

/**
 * @brief 重置SH36760模块状态
 */
static void sh36760_manager_reset(void)
{
    printf("SH36760: Module reset completed\n");
}

/**
 * @brief SH36760芯片管理器实例（静态全局变量）
 */
static SH36760_ChipManager sh36760_chip_manager = {
    .interface = &sh36760_chip_interface,
    .info = {
        .module_name = "SH36760 Chip Interface",
        .version = "1.0",
        .is_initialized = 0
    },
    .methods = {
        .init = sh36760_manager_init,
        .get_info = sh36760_manager_get_info,
        .reset = sh36760_manager_reset
    }
};

/**
 * @brief 获取SH36760芯片管理器实例
 * @return SH36760芯片管理器指针
 * @note 返回完整的芯片管理器实例，包含接口和管理方法
 */
SH36760_ChipManager* sh36760_get_chip_manager(void)
{
    return &sh36760_chip_manager;
}

/**
 * @brief 初始化SH36760芯片模块
 * @return 0=成功，-1=失败
 * @note 初始化芯片模块，设置函数指针和默认参数
 */
int sh36760_chip_module_init(void)
{
    sh36760_chip_manager.info.is_initialized = 1;
    return sh36760_chip_manager.methods.init();
}
