#include "sh36760_chip.h"
#include "sh367601xb_chip.h"
#include "../../user_app_main.h"  /* 引入队列定义 */

/**
 * @file sh36760_chip.c
 * @brief SH36760芯片模块实现 - 基于函数指针的芯片操作接口
 * @version 1.0
 * @date 2025
 *
 * 本文件实现了SH36760芯片的统一操作接口，通过函数指针封装：
 * - 复用sh367601xb_chip.c中的核心功能
 * - 提供统一的接口调用方式
 * - 支持扩展功能和模块管理
 */

/* ==========================================扩展功能函数实现========================================== */

/**
 * @brief 获取当前芯片索引
 * @param device 设备实例指针
 * @return 当前芯片索引
 */
unsigned char sh36760_get_current_chip_index(SH367601B_Device* device)
{
    if (device == NULL) return 0;
    return device->chip.data.current_chip_index;
}

/**
 * @brief 获取芯片数量
 * @param device 设备实例指针
 * @return 芯片数量
 */
unsigned char sh36760_get_chip_count(SH367601B_Device* device)
{
    if (device == NULL) return 0;
    return device->chip.data.chip_count;
}

/**
 * @brief 检查写ROM流程状态
 * @param device 设备实例指针
 * @return 1=写ROM流程进行中，0=空闲状态
 */
unsigned char sh36760_is_write_rom_in_progress(SH367601B_Device* device)
{
    if (device == NULL) return 0;
    return device->chip.data.write_rom_in_progress;
}


