#ifndef SH367601XB_TOOL_H
#define SH367601XB_TOOL_H

/**
 * @file user_app_conversion.h
 * @brief BMS数据转换函数库
 * @version 2.0
 * @date 2024
 *
 */

/**
 * @brief 温度表大小
 */
#define NTC_TABLE_SIZE (sizeof(ntc3435) / sizeof(ntc3435[0]))
/* ==========================================数据结构定义========================================== */

/**
 * @brief NTC阻值-温度对照表结构体
 * @note 用于存储NTC热敏电阻的温度-阻值对应关系
 */
typedef struct {
    unsigned char temp;      /* 温度值，单位：℃，通常加100偏移存储 */
    double resist;          /* 对应阻值，单位：kΩ */
} NTC_TypeDef;


/* ==========================================NTC温度转换模块========================================== */
/**
 * @brief NTC温度转换函数（新版本）
 * @note 支持自定义温度偏移和查找表，提供更好的灵活性
 */
/**
 * @brief 通过查表法计算NTC温度
 */
extern char ntc_calculate_temp_from_resistance(double resistance_ohm, char temp_offset_celsius);
/**
 * @brief zy寄存器值转换为高温温度(ROM)
 */
extern char ntc_calculate_temp_from_adc(unsigned short adc_reg_value);
/**
 * @brief zy寄存器值转换为低温温度(ROM)
 */
extern char ntc_calculate_low_temp_from_reg(unsigned char reg_value);
/**
 * @brief 外部温度寄存器值转换温度(RAM)
 */
extern char ntc_calculate_external_temp(unsigned short temp_reg);

/**
 * @brief 从温度查表获取电阻值（新版本，支持自定义参数）
 */
extern double ntc_find_resistance_from_temp(char temp_celsius, char temp_offset_celsius);

/**
 * @brief 高温温度转换为zy寄存器值(ROM)
 */
extern unsigned char ntc_calculate_high_temp_reg(char temp_celsius);

/**
 * @brief 低温温度转换为zy寄存器值(ROM)
 */
extern unsigned char ntc_calculate_low_temp_reg(char temp_celsius);



/* ==========================================电流转换模块========================================== */
/**
 * @brief 电流转换函数
 * @note 支持自定义采样电阻和温度补偿参数
 */
extern int current_calculate_from_adc(unsigned short adc_reg_value, float sampling_resistance_mohm,
                                     float adc_gain, float adc_reference_factor);



/* ==========================================滤波器模块========================================== */
/**
 * @brief 数字滤波器函数（兼容版本）
 * @note 保持原有接口，使用全局状态
 */
extern float hybridFilter(float new_sample);



/* ==========================================电压转换模块========================================== */
/**
 * @brief 电压转换函数（兼容版本）
 * @note 保持原有接口，内部调用新版本函数
 */
extern unsigned short Reg_From_Voltage(unsigned short cell);


extern const NTC_TypeDef ntc3435[150];
#endif /* _SH367601XB_TOOL_H_ */