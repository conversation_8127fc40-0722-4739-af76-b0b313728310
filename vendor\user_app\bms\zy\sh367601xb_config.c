#include "tl_common.h"
#include "sh367601xb_config.h"
#include "sh367601xb.h"

/* ==========================================配置管理模块实现========================================== */

/**
 * @brief 设置设备ID
 * @param self 设备实例指针
 * @param id 设备ID，范围：0x00-0xFF
 */
void sh367601b_config_set_id(*********_Device* self, unsigned char id)
{
    if (self == NULL) return;
    self->write_rom.id = id;
    self->write_flags[0] = 1;
}

/**
 * @brief 设置放电MOSFET强制开启恢复延时控制位
 * @param self 设备实例指针
 * @param enmosr 控制位，0=禁用，1=启用
 */
void sh367601b_config_set_enmosr(*********_Device* self, unsigned char enmosr)
{
    if (self == NULL || enmosr > 0x01) return;
    self->write_rom.enmosr = enmosr;
    self->write_flags[1] = 1;
}

/**
 * @brief 设置退出过充电保护及充电高低温保护功能变化控制位
 * @param self 设备实例指针
 * @param chys 控制位，0=禁用，1=启用
 */
void sh367601b_config_set_chys(*********_Device* self, unsigned char chys)
{
    if (self == NULL || chys > 0x01) return;
    self->write_rom.chys = chys;
    self->write_flags[1] = 1;
}

/**
 * @brief 设置充电状态下充电温度保护延时（配置chys为1时才生效）
 * @param self 设备实例指针
 * @param tc 延时时间，单位：秒，范围：3-17秒，步长2秒
 */
void sh367601b_config_set_tc(*********_Device* self, unsigned char tc)
{
    if (self == NULL) return;
    self->write_rom.tc = (tc - 3) / 2;
    self->write_flags[1] = 1;
}

/**
 * @brief 设置电池串数配置
 * @param self 设备实例指针
 * @param chip_index 芯片索引 (0-MAX_CHIP_COUNT-1)
 * @param cn 电池串数，范围：6-16串（根据芯片类型限制）
 */
void sh367601b_config_set_cn(*********_Device* self, unsigned char chip_index, unsigned char cn)
{
    if (self == NULL) return;
    
    /* 获取指定芯片支持的最大串数 */
    unsigned char max_cells = self->chip.method.get_chip_max_cells(self, chip_index);
    
    /* 边界检查：最小6串，最大根据芯片类型确定 */
    if (cn < 6) {
        cn = 6;
        printf("*********: Battery count too low, set to minimum 6 cells\n");
    } else if (cn > max_cells) {
        cn = max_cells;
        printf("*********: Battery count too high for chip type, set to maximum %d cells\n", max_cells);
    }
    
    self->write_rom.cn = cn - 6;
    self->write_flags[1] = 1;
    printf("*********: Battery count set to %d cells for chip %d\n", cn, chip_index);
}

/**
 * @brief 设置均衡开启条件控制位
 * @param self 设备实例指针
 * @param bals 均衡条件，0=充电时均衡，1=静置时均衡
 */
void sh367601b_config_set_bals(*********_Device* self, unsigned char bals)
{
    if (self == NULL || bals > 0x01) return;
    self->write_rom.bals = bals;
    self->write_flags[2] = 1;
}

/**
 * @brief 设置充电状态检测电压设置位
 * @param self 设备实例指针
 * @param chs 检测电压，0=100mV，1=200mV
 */
void sh367601b_config_set_chs(*********_Device* self, unsigned char chs)
{
    if (self == NULL || chs > 0x01) return;
    self->write_rom.chs = chs;
    self->write_flags[2] = 1;
}

/**
 * @brief 设置放电过流保护自恢复使能位
 * @param self 设备实例指针
 * @param ocra 自恢复使能，0=禁用，1=启用
 */
void sh367601b_config_set_ocra(*********_Device* self, unsigned char ocra)
{
    if (self == NULL || ocra > 0x01) return;
    self->write_rom.ocra = ocra;
    self->write_flags[2] = 1;
}

/**
 * @brief 设置过充电保护恢复条件设置位
 * @param self 设备实例指针
 * @param eovr 恢复条件，0=任意单体电压低于恢复电压，1=所有单体电压低于恢复电压
 */
void sh367601b_config_set_eovr(*********_Device* self, unsigned char eovr)
{
    if (self == NULL || eovr > 0x01) return;
    self->write_rom.eovr = eovr;
    self->write_flags[2] = 1;
}

/**
 * @brief 设置过放电保护恢复条件设置位
 * @param self 设备实例指针
 * @param euvr 恢复条件，0=任意单体电压高于恢复电压，1=所有单体电压高于恢复电压
 */
void sh367601b_config_set_euvr(*********_Device* self, unsigned char euvr)
{
    if (self == NULL || euvr > 0x01) return;
    self->write_rom.euvr = euvr;
    self->write_flags[2] = 1;
}

/**
 * @brief 设置断线保护使能位
 * @param self 设备实例指针
 * @param eow 断线保护，0=禁用，1=启用
 */
void sh367601b_config_set_eow(*********_Device* self, unsigned char eow)
{
    if (self == NULL || eow > 0x01) return;
    self->write_rom.eow = eow;
    self->write_flags[2] = 1;
}

/**
 * @brief 设置TS3温度检测点功能设置位
 * @param self 设备实例指针
 * @param eot3 TS3功能，0=外部温度检测，1=内部温度检测
 */
void sh367601b_config_set_eot3(*********_Device* self, unsigned char eot3)
{
    if (self == NULL || eot3 > 0x01) return;
    self->write_rom.eot3 = eot3;
    self->write_flags[2] = 1;
}

/**
 * @brief 设置充放电MOSFET强制开启使能位
 * @param self 设备实例指针
 * @param enmos MOSFET强制开启，0=禁用，1=启用
 */
void sh367601b_config_set_enmos(*********_Device* self, unsigned char enmos)
{
    if (self == NULL || enmos > 0x01) return;
    self->write_rom.enmos = enmos;
    self->write_flags[2] = 1;
}

/**
 * @brief 设置过充电保护延时
 * @param self 设备实例指针
 * @param ovt 延时设置，0=1s，1=2s，2=4s，3=8s
 */
void sh367601b_config_set_ovt(*********_Device* self, unsigned short ovt)
{
    if (self == NULL) return;
    
    unsigned char reg_value;
    switch (ovt) {
        case 20:  reg_value = 0; break;
        case 100: reg_value = 1; break;
        case 200: reg_value = 2; break;
        case 500: reg_value = 3; break;
        default:  reg_value = 0; break; /* 默认值或无效值 */
    }
    
    self->write_rom.ovt = reg_value;
    self->write_flags[3] = 1;
}

/**
 * @brief 设置过充电保护电压
 * @param self 设备实例指针
 * @param ov 过充电保护电压，单位：mV，范围：3000-5000mV，步长5mV
 */
void sh367601b_config_set_ov(*********_Device* self, unsigned short ov)
{
    if (self == NULL) return;
    self->write_rom.ov = ov / 5;
    self->write_flags[3] = 1;
    self->write_flags[4] = 1;
}

/**
 * @brief 设置过充电恢复电压
 * @param self 设备实例指针
 * @param ovr 过充电恢复电压，单位：mV，范围：3000-5000mV，步长10mV
 */
void sh367601b_config_set_ovr(*********_Device* self, unsigned short ovr)
{
    if (self == NULL) return;
    self->write_rom.ovr = ovr / 10;
    self->write_flags[4] = 1;
    self->write_flags[5] = 1;
}

/**
 * @brief 设置过放电恢复电压
 * @param self 设备实例指针
 * @param uvr 过放电恢复电压，单位：mV，范围：1000-5000mV，步长20mV
 */
void sh367601b_config_set_uvr(*********_Device* self, unsigned short uvr)
{
    if (self == NULL) return;
    self->write_rom.uvr = uvr / 20;
    self->write_flags[5] = 1;
    self->write_flags[6] = 1;
}

/**
 * @brief 设置低电压禁止充电电压
 * @param self 设备实例指针
 * @param lov 低压禁充电压，单位：mV，范围：500-2250mV，步长250mV，0=禁用
 */
void sh367601b_config_set_lov(*********_Device* self, short lov)
{
    if (self == NULL) return;
    if (!lov) self->write_rom.lov = 0;
    else self->write_rom.lov = (lov - 500) / 250;
    self->write_flags[7] = 1;
}

/**
 * @brief 设置均衡进入延时
 * @param self 设备实例指针
 * @param balt 均衡延时，0=10分钟，1=30分钟
 */
void sh367601b_config_set_balt(*********_Device* self, unsigned char balt)
{
    if (self == NULL) return;
    unsigned char reg_value;
    switch (balt) {
        case 5:  reg_value = 0; break;
        case 50: reg_value = 1; break;
        default:  reg_value = 0; break; /* 默认值或无效值 */
    }
    self->write_rom.balt = reg_value;
    self->write_flags[7] = 1;
}

/**
 * @brief 设置过放电保护延时
 * @param self 设备实例指针
 * @param uvt 延时设置，0=1s，1=4s，2=8s，3=16s
 */
void sh367601b_config_set_uvt(*********_Device* self, unsigned short uvt)
{
    if (self == NULL) return;
    
    unsigned char reg_value;
    switch (uvt) {
        case 50:  reg_value = 0; break;
        case 100: reg_value = 1; break;
        case 200: reg_value = 2; break;
        case 500: reg_value = 3; break;
        default:  reg_value = 0; break; /* 默认值或无效值 */
    }
    self->write_rom.uvt = reg_value;
    self->write_flags[7] = 1;
}

/**
 * @brief 设置过放电保护电压
 * @param self 设备实例指针
 * @param uv 过放电保护电压，单位：mV，范围：1000-5000mV，步长10mV
 */
void sh367601b_config_set_uv(*********_Device* self, unsigned short uv)
{
    if (self == NULL) return;
    self->write_rom.uv = uv / 10;
    self->write_flags[7] = 1;
    self->write_flags[8] = 1;
}

/**
 * @brief 设置均衡开启电压
 * @param self 设备实例指针
 * @param balv 均衡开启电压，单位：mV，范围：3000-8000mV，步长20mV
 */
void sh367601b_config_set_balv(*********_Device* self, unsigned short balv)
{
    if (self == NULL) return;
    self->write_rom.balv = balv / 20;
    self->write_flags[9] = 1;
}

/**
 * @brief 设置均衡开启压差
 * @param self 设备实例指针
 * @param bald 均衡压差，0=10mV，1=20mV，2=30mV，3=50mV
 */
void sh367601b_config_set_bald(*********_Device* self, unsigned char bald)
{

    if (self == NULL) return;
    unsigned char reg_value;
    switch (bald) {
        case 0:  reg_value = 0; break;
        case 20: reg_value = 1; break;
        case 30: reg_value = 2; break;
        case 50: reg_value = 3; break;
        default:  reg_value = 0; break; /* 默认值或无效值 */
    }
    self->write_rom.bald = reg_value;
    self->write_flags[10] = 1;
}

/**
 * @brief 设置放电过流1保护电压
 * @param self 设备实例指针
 * @param ocd1v 过流1保护电压，单位：mV，范围：1575-9450mV，步长525mV，0=禁用
 */
void sh367601b_config_set_ocd1v(*********_Device* self, unsigned short ocd1v)
{
    if (self == NULL) return;
    if (!ocd1v) self->write_rom.ocd1v = 0;
    else self->write_rom.ocd1v = (ocd1v - 1575) / 525;
    self->write_flags[10] = 1;
}

/**
 * @brief 设置放电过流1保护延时
 * @param self 设备实例指针
 * @param ocd1t 延时设置，0=8ms，1=20ms，2=40ms，3=80ms
 */
void sh367601b_config_set_ocd1t(*********_Device* self, unsigned short ocd1t)
{
    if (self == NULL) return;
    unsigned char reg_value;
    switch (ocd1t) {
        case 50:  reg_value = 0; break;
        case 100: reg_value = 1; break;
        case 200: reg_value = 2; break;
        case 500: reg_value = 3; break;
        default:  reg_value = 0; break; /* 默认值或无效值 */
    }
    self->write_rom.ocd1t = reg_value;
    self->write_flags[10] = 1;
}

/**
 * @brief 设置短路保护延时
 * @param self 设备实例指针
 * @param sct 延时设置，0=70μs，1=100μs，2=200μs，3=400μs
 */
void sh367601b_config_set_sct(*********_Device* self, unsigned char sct)
{
    if (self == NULL || sct > 0x03) return;
    self->write_rom.sct = sct;
    self->write_flags[11] = 1;
}

/**
 * @brief 设置放电过流2保护电压
 * @param self 设备实例指针
 * @param ocd2v 过流2保护电压档位，0-7对应不同电压等级
 */
void sh367601b_config_set_ocd2v(*********_Device* self, unsigned char ocd2v)
{
    if (self == NULL) return;
    unsigned char reg_value;
    switch (ocd2v) {
        case 40:  reg_value = 0; break;
        case 50: reg_value = 1; break;
        case 70: reg_value = 2; break;
        case 80: reg_value = 3; break;
        case 100: reg_value = 4; break;
        case 120: reg_value = 5; break;
        case 150: reg_value = 6; break;
        case 200: reg_value = 7; break;
        default:  reg_value = 0; break; /* 默认值或无效值 */
    }
    self->write_rom.ocd2v = reg_value;
    self->write_flags[11] = 1;
}

/**
 * @brief 设置放电过流2保护延时
 * @param self 设备实例指针
 * @param ocd2t 延时设置，0=8ms，1=20ms，2=40ms，3=80ms
 */
void sh367601b_config_set_ocd2t(*********_Device* self, short ocd2t)
{
    if (self == NULL) return;
    unsigned char reg_value;
    switch (ocd2t) {
        case 50:  reg_value = 0; break;
        case 100: reg_value = 1; break;
        case 200: reg_value = 2; break;
        case 500: reg_value = 3; break;
        default:  reg_value = 0; break; /* 默认值或无效值 */
    }
    self->write_rom.ocd2t = reg_value;
    self->write_flags[11] = 1;
}

/**
 * @brief 设置充电过流保护电压
 * @param self 设备实例指针
 * @param occv 充电过流保护电压，单位：mV，范围：350-5775mV，步长175mV，0=禁用
 */
void sh367601b_config_set_occv(*********_Device* self, short occv)
{
    if (self == NULL) return;
    if (!occv) self->write_rom.occv = 0;
    else self->write_rom.occv = ((occv - 350) / 175);
    self->write_flags[12] = 1;
}

/**
 * @brief 设置充电过流保护延时
 * @param self 设备实例指针
 * @param occt 延时设置，0=8ms，1=20ms，2=40ms，3=80ms
 */
void sh367601b_config_set_occt(*********_Device* self, unsigned short occt)
{
    if (self == NULL) return;
    unsigned char reg_value;
    switch (occt) {
        case 10:  reg_value = 0; break;
        case 100: reg_value = 1; break;
        case 300: reg_value = 2; break;
        case 1000: reg_value = 3; break;
        default:  reg_value = 0; break; /* 默认值或无效值 */
    }
    self->write_rom.occt = reg_value;
    self->write_flags[12] = 1;
}

/**
 * @brief 设置充电高温保护阈值
 * @param self 设备实例指针
 * @param otc 充电高温保护温度，单位：℃，范围：-40到125℃
 */
void sh367601b_config_set_otc(*********_Device* self, unsigned char otc)
{
    if (self == NULL) return;
    self->write_rom.otc = self->tool.method.calc_high_temp_reg(otc);
    self->write_flags[13] = 1;
}

/**
 * @brief 设置充电高温保护释放阈值
 * @param self 设备实例指针
 * @param otcr 充电高温保护释放温度，单位：℃，范围：-40到125℃
 */
void sh367601b_config_set_otcr(*********_Device* self, unsigned char otcr)
{
    if (self == NULL) return;
    self->write_rom.otcr = self->tool.method.calc_high_temp_reg(otcr);
    self->write_flags[14] = 1;
}

/**
 * @brief 设置放电高温保护阈值
 * @param self 设备实例指针
 * @param otd 放电高温保护温度，单位：℃，范围：-40到125℃
 */
void sh367601b_config_set_otd(*********_Device* self, unsigned char otd)
{
    if (self == NULL) return;
    self->write_rom.otd = self->tool.method.calc_high_temp_reg(otd);
    self->write_flags[15] = 1;
}

/**
 * @brief 设置放电高温保护释放阈值
 * @param self 设备实例指针
 * @param otdr 放电高温保护释放温度，单位：℃，范围：-40到125℃
 */
void sh367601b_config_set_otdr(*********_Device* self, unsigned char otdr)
{
    if (self == NULL) return;
    self->write_rom.otdr = self->tool.method.calc_high_temp_reg(otdr);
    self->write_flags[16] = 1;
}

/**
 * @brief 设置充电低温保护阈值
 * @param self 设备实例指针
 * @param utc 充电低温保护温度，单位：℃，范围：-40到125℃
 */
void sh367601b_config_set_utc(*********_Device* self, unsigned char utc)
{
    if (self == NULL) return;
    self->write_rom.utc = self->tool.method.calc_low_temp_reg(utc);
    self->write_flags[17] = 1;
}

/**
 * @brief 设置充电低温保护释放阈值
 * @param self 设备实例指针
 * @param utcr 充电低温保护释放温度，单位：℃，范围：-40到125℃
 */
void sh367601b_config_set_utcr(*********_Device* self, unsigned char utcr)
{
    if (self == NULL) return;
    self->write_rom.utcr = self->tool.method.calc_low_temp_reg(utcr);
    self->write_flags[18] = 1;
}

/**
 * @brief 设置放电低温保护阈值
 * @param self 设备实例指针
 * @param utd 放电低温保护温度，单位：℃，范围：-40到125℃
 */
void sh367601b_config_set_utd(*********_Device* self, unsigned char utd)
{
    if (self == NULL) return;
    self->write_rom.utd = self->tool.method.calc_low_temp_reg(utd);
    self->write_flags[19] = 1;
}

/**
 * @brief 设置放电低温保护释放阈值
 * @param self 设备实例指针
 * @param utdr 放电低温保护释放温度，单位：℃，范围：-40到125℃
 */
void sh367601b_config_set_utdr(*********_Device* self, unsigned char utdr)
{
    if (self == NULL) return;
    self->write_rom.utdr = self->tool.method.calc_low_temp_reg(utdr);
    self->write_flags[20] = 1;
}

/**
 * @brief 将ROM结构体数据打包成字节数组用于写入EEPROM
 * @param[out] buffer - 输出缓冲区，至少21字节
 * @param[in] rom - ROM结构体指针
 * @note 按照*********芯片手册的寄存器映射进行打包
 */
void sh367601b_config_pack_rom_data(unsigned char *buffer, User_App_Sh3607601x_Rom_TypeDef *rom)
{
    /* 填充字节数组 */
    /* 00H */
    buffer[0] = rom->id;

    /* 01H: enmosr, chys, tc, cn */
    buffer[1] = (rom->enmosr << 7) | (rom->chys << 6) | (rom->tc << 4) | rom->cn;

    /* 02H: bals, chs, ocra, eovr, euvr, eow, eot3, enmos */
    buffer[2] = (rom->bals << 7) | (rom->chs << 6) | (rom->ocra << 5) |
                  (rom->eovr << 4) | (rom->euvr << 3) | (rom->eow << 2) |
                  (rom->eot3 << 1) | rom->enmos;

    /* 03H,04H,05H: ovt, ov, ovr */
    buffer[3] = (rom->ovt << 6) | ((rom->ov >> 4) & 0x3F);
    buffer[4] = ((rom->ov & 0x0F) << 4) | ((rom->ovr >> 8) & 0x01);
    buffer[5] = rom->ovr & 0xFF;

    /* 06H: uvr */
    buffer[6] = rom->uvr;

    /* 07H,08H: lov, balt, uvt, uv */
    buffer[7] = (rom->lov << 5) | (rom->balt << 4) | (rom->uvt << 1) | ((rom->uv >> 8) & 0x01);
    buffer[8] = rom->uv & 0xFF;

    /* 09H: balv */
    buffer[9] = rom->balv;

    /* 0AH: bald, ocd1v, ocd1t */
    buffer[10] = (rom->bald << 6) | (rom->ocd1v << 2) | rom->ocd1t;

    /* 0BH: sct, ocd2v, ocd2t */
    buffer[11] = (rom->sct << 5) | (rom->ocd2v << 2) | rom->ocd2t;

    /* 0CH: occv, occt */
    buffer[12] = (rom->occv << 2) | rom->occt;

    /* 0DH~14H: otc, otcr, otd, otdr, utc, utcr, utd, utdr */
    buffer[13] = rom->otc;
    buffer[14] = rom->otcr;
    buffer[15] = rom->otd;
    buffer[16] = rom->otdr;
    buffer[17] = rom->utc;
    buffer[18] = rom->utcr;
    buffer[19] = rom->utd;
    buffer[20] = rom->utdr;

    printf("ROM data packed for EEPROM write (%d bytes)\n", 21);
}

/**
 * @brief 创建ROM数据副本用于写入操作
 * @param[in] self - *********设备指针
 * @note 将当前ROM数据复制到write_rom中，用于后续的写入操作
 */
void sh367601b_config_create_rom_copy(*********_Device* self)
{
    if (self == NULL) {
        printf("Error: Device pointer is NULL\n");
        return;
    }

    /* 复制ROM数据到写ROM副本 */
    self->write_rom = self->rom[0];

    printf("ROM copy created for write operations\n");
}

