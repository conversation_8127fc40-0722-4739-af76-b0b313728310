# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Produce verbose output by default.
VERBOSE = 1

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\Telink_Project\BMS_Base

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\Telink_Project\BMS_Base\cmake_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\Telink_Project\BMS_Base\cmake_build\CMakeFiles D:\Telink_Project\BMS_Base\cmake_build\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\Telink_Project\BMS_Base\cmake_build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named 825x_ble_sample

# Build rule for target.
825x_ble_sample: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 825x_ble_sample
.PHONY : 825x_ble_sample

# fast build rule for target.
825x_ble_sample/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/build
.PHONY : 825x_ble_sample/fast

application/app/usbaud.obj: application/app/usbaud.c.obj
.PHONY : application/app/usbaud.obj

# target to build an object file
application/app/usbaud.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/app/usbaud.c.obj
.PHONY : application/app/usbaud.c.obj

application/app/usbaud.i: application/app/usbaud.c.i
.PHONY : application/app/usbaud.i

# target to preprocess a source file
application/app/usbaud.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/app/usbaud.c.i
.PHONY : application/app/usbaud.c.i

application/app/usbaud.s: application/app/usbaud.c.s
.PHONY : application/app/usbaud.s

# target to generate assembly for a file
application/app/usbaud.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/app/usbaud.c.s
.PHONY : application/app/usbaud.c.s

application/app/usbcdc.obj: application/app/usbcdc.c.obj
.PHONY : application/app/usbcdc.obj

# target to build an object file
application/app/usbcdc.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/app/usbcdc.c.obj
.PHONY : application/app/usbcdc.c.obj

application/app/usbcdc.i: application/app/usbcdc.c.i
.PHONY : application/app/usbcdc.i

# target to preprocess a source file
application/app/usbcdc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/app/usbcdc.c.i
.PHONY : application/app/usbcdc.c.i

application/app/usbcdc.s: application/app/usbcdc.c.s
.PHONY : application/app/usbcdc.s

# target to generate assembly for a file
application/app/usbcdc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/app/usbcdc.c.s
.PHONY : application/app/usbcdc.c.s

application/app/usbkb.obj: application/app/usbkb.c.obj
.PHONY : application/app/usbkb.obj

# target to build an object file
application/app/usbkb.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/app/usbkb.c.obj
.PHONY : application/app/usbkb.c.obj

application/app/usbkb.i: application/app/usbkb.c.i
.PHONY : application/app/usbkb.i

# target to preprocess a source file
application/app/usbkb.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/app/usbkb.c.i
.PHONY : application/app/usbkb.c.i

application/app/usbkb.s: application/app/usbkb.c.s
.PHONY : application/app/usbkb.s

# target to generate assembly for a file
application/app/usbkb.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/app/usbkb.c.s
.PHONY : application/app/usbkb.c.s

application/app/usbmouse.obj: application/app/usbmouse.c.obj
.PHONY : application/app/usbmouse.obj

# target to build an object file
application/app/usbmouse.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/app/usbmouse.c.obj
.PHONY : application/app/usbmouse.c.obj

application/app/usbmouse.i: application/app/usbmouse.c.i
.PHONY : application/app/usbmouse.i

# target to preprocess a source file
application/app/usbmouse.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/app/usbmouse.c.i
.PHONY : application/app/usbmouse.c.i

application/app/usbmouse.s: application/app/usbmouse.c.s
.PHONY : application/app/usbmouse.s

# target to generate assembly for a file
application/app/usbmouse.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/app/usbmouse.c.s
.PHONY : application/app/usbmouse.c.s

application/audio/adpcm.obj: application/audio/adpcm.c.obj
.PHONY : application/audio/adpcm.obj

# target to build an object file
application/audio/adpcm.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/audio/adpcm.c.obj
.PHONY : application/audio/adpcm.c.obj

application/audio/adpcm.i: application/audio/adpcm.c.i
.PHONY : application/audio/adpcm.i

# target to preprocess a source file
application/audio/adpcm.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/audio/adpcm.c.i
.PHONY : application/audio/adpcm.c.i

application/audio/adpcm.s: application/audio/adpcm.c.s
.PHONY : application/audio/adpcm.s

# target to generate assembly for a file
application/audio/adpcm.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/audio/adpcm.c.s
.PHONY : application/audio/adpcm.c.s

application/audio/gl_audio.obj: application/audio/gl_audio.c.obj
.PHONY : application/audio/gl_audio.obj

# target to build an object file
application/audio/gl_audio.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/audio/gl_audio.c.obj
.PHONY : application/audio/gl_audio.c.obj

application/audio/gl_audio.i: application/audio/gl_audio.c.i
.PHONY : application/audio/gl_audio.i

# target to preprocess a source file
application/audio/gl_audio.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/audio/gl_audio.c.i
.PHONY : application/audio/gl_audio.c.i

application/audio/gl_audio.s: application/audio/gl_audio.c.s
.PHONY : application/audio/gl_audio.s

# target to generate assembly for a file
application/audio/gl_audio.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/audio/gl_audio.c.s
.PHONY : application/audio/gl_audio.c.s

application/audio/tl_audio.obj: application/audio/tl_audio.c.obj
.PHONY : application/audio/tl_audio.obj

# target to build an object file
application/audio/tl_audio.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/audio/tl_audio.c.obj
.PHONY : application/audio/tl_audio.c.obj

application/audio/tl_audio.i: application/audio/tl_audio.c.i
.PHONY : application/audio/tl_audio.i

# target to preprocess a source file
application/audio/tl_audio.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/audio/tl_audio.c.i
.PHONY : application/audio/tl_audio.c.i

application/audio/tl_audio.s: application/audio/tl_audio.c.s
.PHONY : application/audio/tl_audio.s

# target to generate assembly for a file
application/audio/tl_audio.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/audio/tl_audio.c.s
.PHONY : application/audio/tl_audio.c.s

application/keyboard/keyboard.obj: application/keyboard/keyboard.c.obj
.PHONY : application/keyboard/keyboard.obj

# target to build an object file
application/keyboard/keyboard.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/keyboard/keyboard.c.obj
.PHONY : application/keyboard/keyboard.c.obj

application/keyboard/keyboard.i: application/keyboard/keyboard.c.i
.PHONY : application/keyboard/keyboard.i

# target to preprocess a source file
application/keyboard/keyboard.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/keyboard/keyboard.c.i
.PHONY : application/keyboard/keyboard.c.i

application/keyboard/keyboard.s: application/keyboard/keyboard.c.s
.PHONY : application/keyboard/keyboard.s

# target to generate assembly for a file
application/keyboard/keyboard.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/keyboard/keyboard.c.s
.PHONY : application/keyboard/keyboard.c.s

application/print/putchar.obj: application/print/putchar.c.obj
.PHONY : application/print/putchar.obj

# target to build an object file
application/print/putchar.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/print/putchar.c.obj
.PHONY : application/print/putchar.c.obj

application/print/putchar.i: application/print/putchar.c.i
.PHONY : application/print/putchar.i

# target to preprocess a source file
application/print/putchar.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/print/putchar.c.i
.PHONY : application/print/putchar.c.i

application/print/putchar.s: application/print/putchar.c.s
.PHONY : application/print/putchar.s

# target to generate assembly for a file
application/print/putchar.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/print/putchar.c.s
.PHONY : application/print/putchar.c.s

application/print/u_printf.obj: application/print/u_printf.c.obj
.PHONY : application/print/u_printf.obj

# target to build an object file
application/print/u_printf.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/print/u_printf.c.obj
.PHONY : application/print/u_printf.c.obj

application/print/u_printf.i: application/print/u_printf.c.i
.PHONY : application/print/u_printf.i

# target to preprocess a source file
application/print/u_printf.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/print/u_printf.c.i
.PHONY : application/print/u_printf.c.i

application/print/u_printf.s: application/print/u_printf.c.s
.PHONY : application/print/u_printf.s

# target to generate assembly for a file
application/print/u_printf.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/print/u_printf.c.s
.PHONY : application/print/u_printf.c.s

application/usbstd/usb.obj: application/usbstd/usb.c.obj
.PHONY : application/usbstd/usb.obj

# target to build an object file
application/usbstd/usb.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/usbstd/usb.c.obj
.PHONY : application/usbstd/usb.c.obj

application/usbstd/usb.i: application/usbstd/usb.c.i
.PHONY : application/usbstd/usb.i

# target to preprocess a source file
application/usbstd/usb.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/usbstd/usb.c.i
.PHONY : application/usbstd/usb.c.i

application/usbstd/usb.s: application/usbstd/usb.c.s
.PHONY : application/usbstd/usb.s

# target to generate assembly for a file
application/usbstd/usb.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/usbstd/usb.c.s
.PHONY : application/usbstd/usb.c.s

application/usbstd/usbdesc.obj: application/usbstd/usbdesc.c.obj
.PHONY : application/usbstd/usbdesc.obj

# target to build an object file
application/usbstd/usbdesc.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/usbstd/usbdesc.c.obj
.PHONY : application/usbstd/usbdesc.c.obj

application/usbstd/usbdesc.i: application/usbstd/usbdesc.c.i
.PHONY : application/usbstd/usbdesc.i

# target to preprocess a source file
application/usbstd/usbdesc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/usbstd/usbdesc.c.i
.PHONY : application/usbstd/usbdesc.c.i

application/usbstd/usbdesc.s: application/usbstd/usbdesc.c.s
.PHONY : application/usbstd/usbdesc.s

# target to generate assembly for a file
application/usbstd/usbdesc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/application/usbstd/usbdesc.c.s
.PHONY : application/usbstd/usbdesc.c.s

boot/B85/cstartup_825x.obj: boot/B85/cstartup_825x.S.obj
.PHONY : boot/B85/cstartup_825x.obj

# target to build an object file
boot/B85/cstartup_825x.S.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/boot/B85/cstartup_825x.S.obj
.PHONY : boot/B85/cstartup_825x.S.obj

common/sdk_version.obj: common/sdk_version.c.obj
.PHONY : common/sdk_version.obj

# target to build an object file
common/sdk_version.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/common/sdk_version.c.obj
.PHONY : common/sdk_version.c.obj

common/sdk_version.i: common/sdk_version.c.i
.PHONY : common/sdk_version.i

# target to preprocess a source file
common/sdk_version.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/common/sdk_version.c.i
.PHONY : common/sdk_version.c.i

common/sdk_version.s: common/sdk_version.c.s
.PHONY : common/sdk_version.s

# target to generate assembly for a file
common/sdk_version.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/common/sdk_version.c.s
.PHONY : common/sdk_version.c.s

common/string.obj: common/string.c.obj
.PHONY : common/string.obj

# target to build an object file
common/string.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/common/string.c.obj
.PHONY : common/string.c.obj

common/string.i: common/string.c.i
.PHONY : common/string.i

# target to preprocess a source file
common/string.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/common/string.c.i
.PHONY : common/string.c.i

common/string.s: common/string.c.s
.PHONY : common/string.s

# target to generate assembly for a file
common/string.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/common/string.c.s
.PHONY : common/string.c.s

common/utility.obj: common/utility.c.obj
.PHONY : common/utility.obj

# target to build an object file
common/utility.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/common/utility.c.obj
.PHONY : common/utility.c.obj

common/utility.i: common/utility.c.i
.PHONY : common/utility.i

# target to preprocess a source file
common/utility.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/common/utility.c.i
.PHONY : common/utility.c.i

common/utility.s: common/utility.c.s
.PHONY : common/utility.s

# target to generate assembly for a file
common/utility.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/common/utility.c.s
.PHONY : common/utility.c.s

div_mod.obj: div_mod.S.obj
.PHONY : div_mod.obj

# target to build an object file
div_mod.S.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/div_mod.S.obj
.PHONY : div_mod.S.obj

drivers/8258/adc.obj: drivers/8258/adc.c.obj
.PHONY : drivers/8258/adc.obj

# target to build an object file
drivers/8258/adc.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/adc.c.obj
.PHONY : drivers/8258/adc.c.obj

drivers/8258/adc.i: drivers/8258/adc.c.i
.PHONY : drivers/8258/adc.i

# target to preprocess a source file
drivers/8258/adc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/adc.c.i
.PHONY : drivers/8258/adc.c.i

drivers/8258/adc.s: drivers/8258/adc.c.s
.PHONY : drivers/8258/adc.s

# target to generate assembly for a file
drivers/8258/adc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/adc.c.s
.PHONY : drivers/8258/adc.c.s

drivers/8258/aes.obj: drivers/8258/aes.c.obj
.PHONY : drivers/8258/aes.obj

# target to build an object file
drivers/8258/aes.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/aes.c.obj
.PHONY : drivers/8258/aes.c.obj

drivers/8258/aes.i: drivers/8258/aes.c.i
.PHONY : drivers/8258/aes.i

# target to preprocess a source file
drivers/8258/aes.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/aes.c.i
.PHONY : drivers/8258/aes.c.i

drivers/8258/aes.s: drivers/8258/aes.c.s
.PHONY : drivers/8258/aes.s

# target to generate assembly for a file
drivers/8258/aes.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/aes.c.s
.PHONY : drivers/8258/aes.c.s

drivers/8258/analog.obj: drivers/8258/analog.c.obj
.PHONY : drivers/8258/analog.obj

# target to build an object file
drivers/8258/analog.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/analog.c.obj
.PHONY : drivers/8258/analog.c.obj

drivers/8258/analog.i: drivers/8258/analog.c.i
.PHONY : drivers/8258/analog.i

# target to preprocess a source file
drivers/8258/analog.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/analog.c.i
.PHONY : drivers/8258/analog.c.i

drivers/8258/analog.s: drivers/8258/analog.c.s
.PHONY : drivers/8258/analog.s

# target to generate assembly for a file
drivers/8258/analog.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/analog.c.s
.PHONY : drivers/8258/analog.c.s

drivers/8258/audio.obj: drivers/8258/audio.c.obj
.PHONY : drivers/8258/audio.obj

# target to build an object file
drivers/8258/audio.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/audio.c.obj
.PHONY : drivers/8258/audio.c.obj

drivers/8258/audio.i: drivers/8258/audio.c.i
.PHONY : drivers/8258/audio.i

# target to preprocess a source file
drivers/8258/audio.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/audio.c.i
.PHONY : drivers/8258/audio.c.i

drivers/8258/audio.s: drivers/8258/audio.c.s
.PHONY : drivers/8258/audio.s

# target to generate assembly for a file
drivers/8258/audio.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/audio.c.s
.PHONY : drivers/8258/audio.c.s

drivers/8258/bsp.obj: drivers/8258/bsp.c.obj
.PHONY : drivers/8258/bsp.obj

# target to build an object file
drivers/8258/bsp.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/bsp.c.obj
.PHONY : drivers/8258/bsp.c.obj

drivers/8258/bsp.i: drivers/8258/bsp.c.i
.PHONY : drivers/8258/bsp.i

# target to preprocess a source file
drivers/8258/bsp.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/bsp.c.i
.PHONY : drivers/8258/bsp.c.i

drivers/8258/bsp.s: drivers/8258/bsp.c.s
.PHONY : drivers/8258/bsp.s

# target to generate assembly for a file
drivers/8258/bsp.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/bsp.c.s
.PHONY : drivers/8258/bsp.c.s

drivers/8258/clock.obj: drivers/8258/clock.c.obj
.PHONY : drivers/8258/clock.obj

# target to build an object file
drivers/8258/clock.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/clock.c.obj
.PHONY : drivers/8258/clock.c.obj

drivers/8258/clock.i: drivers/8258/clock.c.i
.PHONY : drivers/8258/clock.i

# target to preprocess a source file
drivers/8258/clock.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/clock.c.i
.PHONY : drivers/8258/clock.c.i

drivers/8258/clock.s: drivers/8258/clock.c.s
.PHONY : drivers/8258/clock.s

# target to generate assembly for a file
drivers/8258/clock.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/clock.c.s
.PHONY : drivers/8258/clock.c.s

drivers/8258/driver_ext/ext_calibration.obj: drivers/8258/driver_ext/ext_calibration.c.obj
.PHONY : drivers/8258/driver_ext/ext_calibration.obj

# target to build an object file
drivers/8258/driver_ext/ext_calibration.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_calibration.c.obj
.PHONY : drivers/8258/driver_ext/ext_calibration.c.obj

drivers/8258/driver_ext/ext_calibration.i: drivers/8258/driver_ext/ext_calibration.c.i
.PHONY : drivers/8258/driver_ext/ext_calibration.i

# target to preprocess a source file
drivers/8258/driver_ext/ext_calibration.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_calibration.c.i
.PHONY : drivers/8258/driver_ext/ext_calibration.c.i

drivers/8258/driver_ext/ext_calibration.s: drivers/8258/driver_ext/ext_calibration.c.s
.PHONY : drivers/8258/driver_ext/ext_calibration.s

# target to generate assembly for a file
drivers/8258/driver_ext/ext_calibration.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_calibration.c.s
.PHONY : drivers/8258/driver_ext/ext_calibration.c.s

drivers/8258/driver_ext/ext_misc.obj: drivers/8258/driver_ext/ext_misc.c.obj
.PHONY : drivers/8258/driver_ext/ext_misc.obj

# target to build an object file
drivers/8258/driver_ext/ext_misc.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_misc.c.obj
.PHONY : drivers/8258/driver_ext/ext_misc.c.obj

drivers/8258/driver_ext/ext_misc.i: drivers/8258/driver_ext/ext_misc.c.i
.PHONY : drivers/8258/driver_ext/ext_misc.i

# target to preprocess a source file
drivers/8258/driver_ext/ext_misc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_misc.c.i
.PHONY : drivers/8258/driver_ext/ext_misc.c.i

drivers/8258/driver_ext/ext_misc.s: drivers/8258/driver_ext/ext_misc.c.s
.PHONY : drivers/8258/driver_ext/ext_misc.s

# target to generate assembly for a file
drivers/8258/driver_ext/ext_misc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_misc.c.s
.PHONY : drivers/8258/driver_ext/ext_misc.c.s

drivers/8258/driver_ext/rf_pa.obj: drivers/8258/driver_ext/rf_pa.c.obj
.PHONY : drivers/8258/driver_ext/rf_pa.obj

# target to build an object file
drivers/8258/driver_ext/rf_pa.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/rf_pa.c.obj
.PHONY : drivers/8258/driver_ext/rf_pa.c.obj

drivers/8258/driver_ext/rf_pa.i: drivers/8258/driver_ext/rf_pa.c.i
.PHONY : drivers/8258/driver_ext/rf_pa.i

# target to preprocess a source file
drivers/8258/driver_ext/rf_pa.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/rf_pa.c.i
.PHONY : drivers/8258/driver_ext/rf_pa.c.i

drivers/8258/driver_ext/rf_pa.s: drivers/8258/driver_ext/rf_pa.c.s
.PHONY : drivers/8258/driver_ext/rf_pa.s

# target to generate assembly for a file
drivers/8258/driver_ext/rf_pa.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/rf_pa.c.s
.PHONY : drivers/8258/driver_ext/rf_pa.c.s

drivers/8258/driver_ext/software_uart.obj: drivers/8258/driver_ext/software_uart.c.obj
.PHONY : drivers/8258/driver_ext/software_uart.obj

# target to build an object file
drivers/8258/driver_ext/software_uart.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/software_uart.c.obj
.PHONY : drivers/8258/driver_ext/software_uart.c.obj

drivers/8258/driver_ext/software_uart.i: drivers/8258/driver_ext/software_uart.c.i
.PHONY : drivers/8258/driver_ext/software_uart.i

# target to preprocess a source file
drivers/8258/driver_ext/software_uart.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/software_uart.c.i
.PHONY : drivers/8258/driver_ext/software_uart.c.i

drivers/8258/driver_ext/software_uart.s: drivers/8258/driver_ext/software_uart.c.s
.PHONY : drivers/8258/driver_ext/software_uart.s

# target to generate assembly for a file
drivers/8258/driver_ext/software_uart.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/software_uart.c.s
.PHONY : drivers/8258/driver_ext/software_uart.c.s

drivers/8258/emi.obj: drivers/8258/emi.c.obj
.PHONY : drivers/8258/emi.obj

# target to build an object file
drivers/8258/emi.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/emi.c.obj
.PHONY : drivers/8258/emi.c.obj

drivers/8258/emi.i: drivers/8258/emi.c.i
.PHONY : drivers/8258/emi.i

# target to preprocess a source file
drivers/8258/emi.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/emi.c.i
.PHONY : drivers/8258/emi.c.i

drivers/8258/emi.s: drivers/8258/emi.c.s
.PHONY : drivers/8258/emi.s

# target to generate assembly for a file
drivers/8258/emi.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/emi.c.s
.PHONY : drivers/8258/emi.c.s

drivers/8258/flash.obj: drivers/8258/flash.c.obj
.PHONY : drivers/8258/flash.obj

# target to build an object file
drivers/8258/flash.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash.c.obj
.PHONY : drivers/8258/flash.c.obj

drivers/8258/flash.i: drivers/8258/flash.c.i
.PHONY : drivers/8258/flash.i

# target to preprocess a source file
drivers/8258/flash.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash.c.i
.PHONY : drivers/8258/flash.c.i

drivers/8258/flash.s: drivers/8258/flash.c.s
.PHONY : drivers/8258/flash.s

# target to generate assembly for a file
drivers/8258/flash.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash.c.s
.PHONY : drivers/8258/flash.c.s

drivers/8258/flash/flash_mid011460c8.obj: drivers/8258/flash/flash_mid011460c8.c.obj
.PHONY : drivers/8258/flash/flash_mid011460c8.obj

# target to build an object file
drivers/8258/flash/flash_mid011460c8.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid011460c8.c.obj
.PHONY : drivers/8258/flash/flash_mid011460c8.c.obj

drivers/8258/flash/flash_mid011460c8.i: drivers/8258/flash/flash_mid011460c8.c.i
.PHONY : drivers/8258/flash/flash_mid011460c8.i

# target to preprocess a source file
drivers/8258/flash/flash_mid011460c8.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid011460c8.c.i
.PHONY : drivers/8258/flash/flash_mid011460c8.c.i

drivers/8258/flash/flash_mid011460c8.s: drivers/8258/flash/flash_mid011460c8.c.s
.PHONY : drivers/8258/flash/flash_mid011460c8.s

# target to generate assembly for a file
drivers/8258/flash/flash_mid011460c8.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid011460c8.c.s
.PHONY : drivers/8258/flash/flash_mid011460c8.c.s

drivers/8258/flash/flash_mid1060c8.obj: drivers/8258/flash/flash_mid1060c8.c.obj
.PHONY : drivers/8258/flash/flash_mid1060c8.obj

# target to build an object file
drivers/8258/flash/flash_mid1060c8.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1060c8.c.obj
.PHONY : drivers/8258/flash/flash_mid1060c8.c.obj

drivers/8258/flash/flash_mid1060c8.i: drivers/8258/flash/flash_mid1060c8.c.i
.PHONY : drivers/8258/flash/flash_mid1060c8.i

# target to preprocess a source file
drivers/8258/flash/flash_mid1060c8.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1060c8.c.i
.PHONY : drivers/8258/flash/flash_mid1060c8.c.i

drivers/8258/flash/flash_mid1060c8.s: drivers/8258/flash/flash_mid1060c8.c.s
.PHONY : drivers/8258/flash/flash_mid1060c8.s

# target to generate assembly for a file
drivers/8258/flash/flash_mid1060c8.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1060c8.c.s
.PHONY : drivers/8258/flash/flash_mid1060c8.c.s

drivers/8258/flash/flash_mid13325e.obj: drivers/8258/flash/flash_mid13325e.c.obj
.PHONY : drivers/8258/flash/flash_mid13325e.obj

# target to build an object file
drivers/8258/flash/flash_mid13325e.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid13325e.c.obj
.PHONY : drivers/8258/flash/flash_mid13325e.c.obj

drivers/8258/flash/flash_mid13325e.i: drivers/8258/flash/flash_mid13325e.c.i
.PHONY : drivers/8258/flash/flash_mid13325e.i

# target to preprocess a source file
drivers/8258/flash/flash_mid13325e.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid13325e.c.i
.PHONY : drivers/8258/flash/flash_mid13325e.c.i

drivers/8258/flash/flash_mid13325e.s: drivers/8258/flash/flash_mid13325e.c.s
.PHONY : drivers/8258/flash/flash_mid13325e.s

# target to generate assembly for a file
drivers/8258/flash/flash_mid13325e.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid13325e.c.s
.PHONY : drivers/8258/flash/flash_mid13325e.c.s

drivers/8258/flash/flash_mid134051.obj: drivers/8258/flash/flash_mid134051.c.obj
.PHONY : drivers/8258/flash/flash_mid134051.obj

# target to build an object file
drivers/8258/flash/flash_mid134051.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid134051.c.obj
.PHONY : drivers/8258/flash/flash_mid134051.c.obj

drivers/8258/flash/flash_mid134051.i: drivers/8258/flash/flash_mid134051.c.i
.PHONY : drivers/8258/flash/flash_mid134051.i

# target to preprocess a source file
drivers/8258/flash/flash_mid134051.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid134051.c.i
.PHONY : drivers/8258/flash/flash_mid134051.c.i

drivers/8258/flash/flash_mid134051.s: drivers/8258/flash/flash_mid134051.c.s
.PHONY : drivers/8258/flash/flash_mid134051.s

# target to generate assembly for a file
drivers/8258/flash/flash_mid134051.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid134051.c.s
.PHONY : drivers/8258/flash/flash_mid134051.c.s

drivers/8258/flash/flash_mid136085.obj: drivers/8258/flash/flash_mid136085.c.obj
.PHONY : drivers/8258/flash/flash_mid136085.obj

# target to build an object file
drivers/8258/flash/flash_mid136085.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid136085.c.obj
.PHONY : drivers/8258/flash/flash_mid136085.c.obj

drivers/8258/flash/flash_mid136085.i: drivers/8258/flash/flash_mid136085.c.i
.PHONY : drivers/8258/flash/flash_mid136085.i

# target to preprocess a source file
drivers/8258/flash/flash_mid136085.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid136085.c.i
.PHONY : drivers/8258/flash/flash_mid136085.c.i

drivers/8258/flash/flash_mid136085.s: drivers/8258/flash/flash_mid136085.c.s
.PHONY : drivers/8258/flash/flash_mid136085.s

# target to generate assembly for a file
drivers/8258/flash/flash_mid136085.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid136085.c.s
.PHONY : drivers/8258/flash/flash_mid136085.c.s

drivers/8258/flash/flash_mid1360c8.obj: drivers/8258/flash/flash_mid1360c8.c.obj
.PHONY : drivers/8258/flash/flash_mid1360c8.obj

# target to build an object file
drivers/8258/flash/flash_mid1360c8.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360c8.c.obj
.PHONY : drivers/8258/flash/flash_mid1360c8.c.obj

drivers/8258/flash/flash_mid1360c8.i: drivers/8258/flash/flash_mid1360c8.c.i
.PHONY : drivers/8258/flash/flash_mid1360c8.i

# target to preprocess a source file
drivers/8258/flash/flash_mid1360c8.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360c8.c.i
.PHONY : drivers/8258/flash/flash_mid1360c8.c.i

drivers/8258/flash/flash_mid1360c8.s: drivers/8258/flash/flash_mid1360c8.c.s
.PHONY : drivers/8258/flash/flash_mid1360c8.s

# target to generate assembly for a file
drivers/8258/flash/flash_mid1360c8.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360c8.c.s
.PHONY : drivers/8258/flash/flash_mid1360c8.c.s

drivers/8258/flash/flash_mid1360eb.obj: drivers/8258/flash/flash_mid1360eb.c.obj
.PHONY : drivers/8258/flash/flash_mid1360eb.obj

# target to build an object file
drivers/8258/flash/flash_mid1360eb.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360eb.c.obj
.PHONY : drivers/8258/flash/flash_mid1360eb.c.obj

drivers/8258/flash/flash_mid1360eb.i: drivers/8258/flash/flash_mid1360eb.c.i
.PHONY : drivers/8258/flash/flash_mid1360eb.i

# target to preprocess a source file
drivers/8258/flash/flash_mid1360eb.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360eb.c.i
.PHONY : drivers/8258/flash/flash_mid1360eb.c.i

drivers/8258/flash/flash_mid1360eb.s: drivers/8258/flash/flash_mid1360eb.c.s
.PHONY : drivers/8258/flash/flash_mid1360eb.s

# target to generate assembly for a file
drivers/8258/flash/flash_mid1360eb.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360eb.c.s
.PHONY : drivers/8258/flash/flash_mid1360eb.c.s

drivers/8258/flash/flash_mid14325e.obj: drivers/8258/flash/flash_mid14325e.c.obj
.PHONY : drivers/8258/flash/flash_mid14325e.obj

# target to build an object file
drivers/8258/flash/flash_mid14325e.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid14325e.c.obj
.PHONY : drivers/8258/flash/flash_mid14325e.c.obj

drivers/8258/flash/flash_mid14325e.i: drivers/8258/flash/flash_mid14325e.c.i
.PHONY : drivers/8258/flash/flash_mid14325e.i

# target to preprocess a source file
drivers/8258/flash/flash_mid14325e.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid14325e.c.i
.PHONY : drivers/8258/flash/flash_mid14325e.c.i

drivers/8258/flash/flash_mid14325e.s: drivers/8258/flash/flash_mid14325e.c.s
.PHONY : drivers/8258/flash/flash_mid14325e.s

# target to generate assembly for a file
drivers/8258/flash/flash_mid14325e.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid14325e.c.s
.PHONY : drivers/8258/flash/flash_mid14325e.c.s

drivers/8258/flash/flash_mid1460c8.obj: drivers/8258/flash/flash_mid1460c8.c.obj
.PHONY : drivers/8258/flash/flash_mid1460c8.obj

# target to build an object file
drivers/8258/flash/flash_mid1460c8.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1460c8.c.obj
.PHONY : drivers/8258/flash/flash_mid1460c8.c.obj

drivers/8258/flash/flash_mid1460c8.i: drivers/8258/flash/flash_mid1460c8.c.i
.PHONY : drivers/8258/flash/flash_mid1460c8.i

# target to preprocess a source file
drivers/8258/flash/flash_mid1460c8.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1460c8.c.i
.PHONY : drivers/8258/flash/flash_mid1460c8.c.i

drivers/8258/flash/flash_mid1460c8.s: drivers/8258/flash/flash_mid1460c8.c.s
.PHONY : drivers/8258/flash/flash_mid1460c8.s

# target to generate assembly for a file
drivers/8258/flash/flash_mid1460c8.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1460c8.c.s
.PHONY : drivers/8258/flash/flash_mid1460c8.c.s

drivers/8258/gpio.obj: drivers/8258/gpio.c.obj
.PHONY : drivers/8258/gpio.obj

# target to build an object file
drivers/8258/gpio.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/gpio.c.obj
.PHONY : drivers/8258/gpio.c.obj

drivers/8258/gpio.i: drivers/8258/gpio.c.i
.PHONY : drivers/8258/gpio.i

# target to preprocess a source file
drivers/8258/gpio.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/gpio.c.i
.PHONY : drivers/8258/gpio.c.i

drivers/8258/gpio.s: drivers/8258/gpio.c.s
.PHONY : drivers/8258/gpio.s

# target to generate assembly for a file
drivers/8258/gpio.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/gpio.c.s
.PHONY : drivers/8258/gpio.c.s

drivers/8258/i2c.obj: drivers/8258/i2c.c.obj
.PHONY : drivers/8258/i2c.obj

# target to build an object file
drivers/8258/i2c.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/i2c.c.obj
.PHONY : drivers/8258/i2c.c.obj

drivers/8258/i2c.i: drivers/8258/i2c.c.i
.PHONY : drivers/8258/i2c.i

# target to preprocess a source file
drivers/8258/i2c.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/i2c.c.i
.PHONY : drivers/8258/i2c.c.i

drivers/8258/i2c.s: drivers/8258/i2c.c.s
.PHONY : drivers/8258/i2c.s

# target to generate assembly for a file
drivers/8258/i2c.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/i2c.c.s
.PHONY : drivers/8258/i2c.c.s

drivers/8258/lpc.obj: drivers/8258/lpc.c.obj
.PHONY : drivers/8258/lpc.obj

# target to build an object file
drivers/8258/lpc.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/lpc.c.obj
.PHONY : drivers/8258/lpc.c.obj

drivers/8258/lpc.i: drivers/8258/lpc.c.i
.PHONY : drivers/8258/lpc.i

# target to preprocess a source file
drivers/8258/lpc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/lpc.c.i
.PHONY : drivers/8258/lpc.c.i

drivers/8258/lpc.s: drivers/8258/lpc.c.s
.PHONY : drivers/8258/lpc.s

# target to generate assembly for a file
drivers/8258/lpc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/lpc.c.s
.PHONY : drivers/8258/lpc.c.s

drivers/8258/qdec.obj: drivers/8258/qdec.c.obj
.PHONY : drivers/8258/qdec.obj

# target to build an object file
drivers/8258/qdec.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/qdec.c.obj
.PHONY : drivers/8258/qdec.c.obj

drivers/8258/qdec.i: drivers/8258/qdec.c.i
.PHONY : drivers/8258/qdec.i

# target to preprocess a source file
drivers/8258/qdec.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/qdec.c.i
.PHONY : drivers/8258/qdec.c.i

drivers/8258/qdec.s: drivers/8258/qdec.c.s
.PHONY : drivers/8258/qdec.s

# target to generate assembly for a file
drivers/8258/qdec.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/qdec.c.s
.PHONY : drivers/8258/qdec.c.s

drivers/8258/s7816.obj: drivers/8258/s7816.c.obj
.PHONY : drivers/8258/s7816.obj

# target to build an object file
drivers/8258/s7816.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/s7816.c.obj
.PHONY : drivers/8258/s7816.c.obj

drivers/8258/s7816.i: drivers/8258/s7816.c.i
.PHONY : drivers/8258/s7816.i

# target to preprocess a source file
drivers/8258/s7816.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/s7816.c.i
.PHONY : drivers/8258/s7816.c.i

drivers/8258/s7816.s: drivers/8258/s7816.c.s
.PHONY : drivers/8258/s7816.s

# target to generate assembly for a file
drivers/8258/s7816.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/s7816.c.s
.PHONY : drivers/8258/s7816.c.s

drivers/8258/spi.obj: drivers/8258/spi.c.obj
.PHONY : drivers/8258/spi.obj

# target to build an object file
drivers/8258/spi.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/spi.c.obj
.PHONY : drivers/8258/spi.c.obj

drivers/8258/spi.i: drivers/8258/spi.c.i
.PHONY : drivers/8258/spi.i

# target to preprocess a source file
drivers/8258/spi.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/spi.c.i
.PHONY : drivers/8258/spi.c.i

drivers/8258/spi.s: drivers/8258/spi.c.s
.PHONY : drivers/8258/spi.s

# target to generate assembly for a file
drivers/8258/spi.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/spi.c.s
.PHONY : drivers/8258/spi.c.s

drivers/8258/timer.obj: drivers/8258/timer.c.obj
.PHONY : drivers/8258/timer.obj

# target to build an object file
drivers/8258/timer.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/timer.c.obj
.PHONY : drivers/8258/timer.c.obj

drivers/8258/timer.i: drivers/8258/timer.c.i
.PHONY : drivers/8258/timer.i

# target to preprocess a source file
drivers/8258/timer.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/timer.c.i
.PHONY : drivers/8258/timer.c.i

drivers/8258/timer.s: drivers/8258/timer.c.s
.PHONY : drivers/8258/timer.s

# target to generate assembly for a file
drivers/8258/timer.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/timer.c.s
.PHONY : drivers/8258/timer.c.s

drivers/8258/uart.obj: drivers/8258/uart.c.obj
.PHONY : drivers/8258/uart.obj

# target to build an object file
drivers/8258/uart.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/uart.c.obj
.PHONY : drivers/8258/uart.c.obj

drivers/8258/uart.i: drivers/8258/uart.c.i
.PHONY : drivers/8258/uart.i

# target to preprocess a source file
drivers/8258/uart.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/uart.c.i
.PHONY : drivers/8258/uart.c.i

drivers/8258/uart.s: drivers/8258/uart.c.s
.PHONY : drivers/8258/uart.s

# target to generate assembly for a file
drivers/8258/uart.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/uart.c.s
.PHONY : drivers/8258/uart.c.s

drivers/8258/usbhw.obj: drivers/8258/usbhw.c.obj
.PHONY : drivers/8258/usbhw.obj

# target to build an object file
drivers/8258/usbhw.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/usbhw.c.obj
.PHONY : drivers/8258/usbhw.c.obj

drivers/8258/usbhw.i: drivers/8258/usbhw.c.i
.PHONY : drivers/8258/usbhw.i

# target to preprocess a source file
drivers/8258/usbhw.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/usbhw.c.i
.PHONY : drivers/8258/usbhw.c.i

drivers/8258/usbhw.s: drivers/8258/usbhw.c.s
.PHONY : drivers/8258/usbhw.s

# target to generate assembly for a file
drivers/8258/usbhw.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/usbhw.c.s
.PHONY : drivers/8258/usbhw.c.s

drivers/8258/watchdog.obj: drivers/8258/watchdog.c.obj
.PHONY : drivers/8258/watchdog.obj

# target to build an object file
drivers/8258/watchdog.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/watchdog.c.obj
.PHONY : drivers/8258/watchdog.c.obj

drivers/8258/watchdog.i: drivers/8258/watchdog.c.i
.PHONY : drivers/8258/watchdog.i

# target to preprocess a source file
drivers/8258/watchdog.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/watchdog.c.i
.PHONY : drivers/8258/watchdog.c.i

drivers/8258/watchdog.s: drivers/8258/watchdog.c.s
.PHONY : drivers/8258/watchdog.s

# target to generate assembly for a file
drivers/8258/watchdog.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/drivers/8258/watchdog.c.s
.PHONY : drivers/8258/watchdog.c.s

vendor/b85m_ble_sample/app.obj: vendor/b85m_ble_sample/app.c.obj
.PHONY : vendor/b85m_ble_sample/app.obj

# target to build an object file
vendor/b85m_ble_sample/app.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app.c.obj
.PHONY : vendor/b85m_ble_sample/app.c.obj

vendor/b85m_ble_sample/app.i: vendor/b85m_ble_sample/app.c.i
.PHONY : vendor/b85m_ble_sample/app.i

# target to preprocess a source file
vendor/b85m_ble_sample/app.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app.c.i
.PHONY : vendor/b85m_ble_sample/app.c.i

vendor/b85m_ble_sample/app.s: vendor/b85m_ble_sample/app.c.s
.PHONY : vendor/b85m_ble_sample/app.s

# target to generate assembly for a file
vendor/b85m_ble_sample/app.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app.c.s
.PHONY : vendor/b85m_ble_sample/app.c.s

vendor/b85m_ble_sample/app_att.obj: vendor/b85m_ble_sample/app_att.c.obj
.PHONY : vendor/b85m_ble_sample/app_att.obj

# target to build an object file
vendor/b85m_ble_sample/app_att.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_att.c.obj
.PHONY : vendor/b85m_ble_sample/app_att.c.obj

vendor/b85m_ble_sample/app_att.i: vendor/b85m_ble_sample/app_att.c.i
.PHONY : vendor/b85m_ble_sample/app_att.i

# target to preprocess a source file
vendor/b85m_ble_sample/app_att.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_att.c.i
.PHONY : vendor/b85m_ble_sample/app_att.c.i

vendor/b85m_ble_sample/app_att.s: vendor/b85m_ble_sample/app_att.c.s
.PHONY : vendor/b85m_ble_sample/app_att.s

# target to generate assembly for a file
vendor/b85m_ble_sample/app_att.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_att.c.s
.PHONY : vendor/b85m_ble_sample/app_att.c.s

vendor/b85m_ble_sample/app_ui.obj: vendor/b85m_ble_sample/app_ui.c.obj
.PHONY : vendor/b85m_ble_sample/app_ui.obj

# target to build an object file
vendor/b85m_ble_sample/app_ui.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_ui.c.obj
.PHONY : vendor/b85m_ble_sample/app_ui.c.obj

vendor/b85m_ble_sample/app_ui.i: vendor/b85m_ble_sample/app_ui.c.i
.PHONY : vendor/b85m_ble_sample/app_ui.i

# target to preprocess a source file
vendor/b85m_ble_sample/app_ui.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_ui.c.i
.PHONY : vendor/b85m_ble_sample/app_ui.c.i

vendor/b85m_ble_sample/app_ui.s: vendor/b85m_ble_sample/app_ui.c.s
.PHONY : vendor/b85m_ble_sample/app_ui.s

# target to generate assembly for a file
vendor/b85m_ble_sample/app_ui.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_ui.c.s
.PHONY : vendor/b85m_ble_sample/app_ui.c.s

vendor/b85m_ble_sample/main.obj: vendor/b85m_ble_sample/main.c.obj
.PHONY : vendor/b85m_ble_sample/main.obj

# target to build an object file
vendor/b85m_ble_sample/main.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/main.c.obj
.PHONY : vendor/b85m_ble_sample/main.c.obj

vendor/b85m_ble_sample/main.i: vendor/b85m_ble_sample/main.c.i
.PHONY : vendor/b85m_ble_sample/main.i

# target to preprocess a source file
vendor/b85m_ble_sample/main.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/main.c.i
.PHONY : vendor/b85m_ble_sample/main.c.i

vendor/b85m_ble_sample/main.s: vendor/b85m_ble_sample/main.c.s
.PHONY : vendor/b85m_ble_sample/main.s

# target to generate assembly for a file
vendor/b85m_ble_sample/main.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/main.c.s
.PHONY : vendor/b85m_ble_sample/main.c.s

vendor/common/app_buffer.obj: vendor/common/app_buffer.c.obj
.PHONY : vendor/common/app_buffer.obj

# target to build an object file
vendor/common/app_buffer.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/app_buffer.c.obj
.PHONY : vendor/common/app_buffer.c.obj

vendor/common/app_buffer.i: vendor/common/app_buffer.c.i
.PHONY : vendor/common/app_buffer.i

# target to preprocess a source file
vendor/common/app_buffer.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/app_buffer.c.i
.PHONY : vendor/common/app_buffer.c.i

vendor/common/app_buffer.s: vendor/common/app_buffer.c.s
.PHONY : vendor/common/app_buffer.s

# target to generate assembly for a file
vendor/common/app_buffer.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/app_buffer.c.s
.PHONY : vendor/common/app_buffer.c.s

vendor/common/app_common.obj: vendor/common/app_common.c.obj
.PHONY : vendor/common/app_common.obj

# target to build an object file
vendor/common/app_common.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/app_common.c.obj
.PHONY : vendor/common/app_common.c.obj

vendor/common/app_common.i: vendor/common/app_common.c.i
.PHONY : vendor/common/app_common.i

# target to preprocess a source file
vendor/common/app_common.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/app_common.c.i
.PHONY : vendor/common/app_common.c.i

vendor/common/app_common.s: vendor/common/app_common.c.s
.PHONY : vendor/common/app_common.s

# target to generate assembly for a file
vendor/common/app_common.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/app_common.c.s
.PHONY : vendor/common/app_common.c.s

vendor/common/battery_check.obj: vendor/common/battery_check.c.obj
.PHONY : vendor/common/battery_check.obj

# target to build an object file
vendor/common/battery_check.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/battery_check.c.obj
.PHONY : vendor/common/battery_check.c.obj

vendor/common/battery_check.i: vendor/common/battery_check.c.i
.PHONY : vendor/common/battery_check.i

# target to preprocess a source file
vendor/common/battery_check.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/battery_check.c.i
.PHONY : vendor/common/battery_check.c.i

vendor/common/battery_check.s: vendor/common/battery_check.c.s
.PHONY : vendor/common/battery_check.s

# target to generate assembly for a file
vendor/common/battery_check.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/battery_check.c.s
.PHONY : vendor/common/battery_check.c.s

vendor/common/ble_flash.obj: vendor/common/ble_flash.c.obj
.PHONY : vendor/common/ble_flash.obj

# target to build an object file
vendor/common/ble_flash.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/ble_flash.c.obj
.PHONY : vendor/common/ble_flash.c.obj

vendor/common/ble_flash.i: vendor/common/ble_flash.c.i
.PHONY : vendor/common/ble_flash.i

# target to preprocess a source file
vendor/common/ble_flash.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/ble_flash.c.i
.PHONY : vendor/common/ble_flash.c.i

vendor/common/ble_flash.s: vendor/common/ble_flash.c.s
.PHONY : vendor/common/ble_flash.s

# target to generate assembly for a file
vendor/common/ble_flash.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/ble_flash.c.s
.PHONY : vendor/common/ble_flash.c.s

vendor/common/blt_fw_sign.obj: vendor/common/blt_fw_sign.c.obj
.PHONY : vendor/common/blt_fw_sign.obj

# target to build an object file
vendor/common/blt_fw_sign.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/blt_fw_sign.c.obj
.PHONY : vendor/common/blt_fw_sign.c.obj

vendor/common/blt_fw_sign.i: vendor/common/blt_fw_sign.c.i
.PHONY : vendor/common/blt_fw_sign.i

# target to preprocess a source file
vendor/common/blt_fw_sign.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/blt_fw_sign.c.i
.PHONY : vendor/common/blt_fw_sign.c.i

vendor/common/blt_fw_sign.s: vendor/common/blt_fw_sign.c.s
.PHONY : vendor/common/blt_fw_sign.s

# target to generate assembly for a file
vendor/common/blt_fw_sign.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/blt_fw_sign.c.s
.PHONY : vendor/common/blt_fw_sign.c.s

vendor/common/blt_led.obj: vendor/common/blt_led.c.obj
.PHONY : vendor/common/blt_led.obj

# target to build an object file
vendor/common/blt_led.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/blt_led.c.obj
.PHONY : vendor/common/blt_led.c.obj

vendor/common/blt_led.i: vendor/common/blt_led.c.i
.PHONY : vendor/common/blt_led.i

# target to preprocess a source file
vendor/common/blt_led.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/blt_led.c.i
.PHONY : vendor/common/blt_led.c.i

vendor/common/blt_led.s: vendor/common/blt_led.c.s
.PHONY : vendor/common/blt_led.s

# target to generate assembly for a file
vendor/common/blt_led.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/blt_led.c.s
.PHONY : vendor/common/blt_led.c.s

vendor/common/blt_soft_timer.obj: vendor/common/blt_soft_timer.c.obj
.PHONY : vendor/common/blt_soft_timer.obj

# target to build an object file
vendor/common/blt_soft_timer.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/blt_soft_timer.c.obj
.PHONY : vendor/common/blt_soft_timer.c.obj

vendor/common/blt_soft_timer.i: vendor/common/blt_soft_timer.c.i
.PHONY : vendor/common/blt_soft_timer.i

# target to preprocess a source file
vendor/common/blt_soft_timer.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/blt_soft_timer.c.i
.PHONY : vendor/common/blt_soft_timer.c.i

vendor/common/blt_soft_timer.s: vendor/common/blt_soft_timer.c.s
.PHONY : vendor/common/blt_soft_timer.s

# target to generate assembly for a file
vendor/common/blt_soft_timer.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/blt_soft_timer.c.s
.PHONY : vendor/common/blt_soft_timer.c.s

vendor/common/custom_pair.obj: vendor/common/custom_pair.c.obj
.PHONY : vendor/common/custom_pair.obj

# target to build an object file
vendor/common/custom_pair.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/custom_pair.c.obj
.PHONY : vendor/common/custom_pair.c.obj

vendor/common/custom_pair.i: vendor/common/custom_pair.c.i
.PHONY : vendor/common/custom_pair.i

# target to preprocess a source file
vendor/common/custom_pair.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/custom_pair.c.i
.PHONY : vendor/common/custom_pair.c.i

vendor/common/custom_pair.s: vendor/common/custom_pair.c.s
.PHONY : vendor/common/custom_pair.s

# target to generate assembly for a file
vendor/common/custom_pair.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/custom_pair.c.s
.PHONY : vendor/common/custom_pair.c.s

vendor/common/flash_fw_check.obj: vendor/common/flash_fw_check.c.obj
.PHONY : vendor/common/flash_fw_check.obj

# target to build an object file
vendor/common/flash_fw_check.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/flash_fw_check.c.obj
.PHONY : vendor/common/flash_fw_check.c.obj

vendor/common/flash_fw_check.i: vendor/common/flash_fw_check.c.i
.PHONY : vendor/common/flash_fw_check.i

# target to preprocess a source file
vendor/common/flash_fw_check.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/flash_fw_check.c.i
.PHONY : vendor/common/flash_fw_check.c.i

vendor/common/flash_fw_check.s: vendor/common/flash_fw_check.c.s
.PHONY : vendor/common/flash_fw_check.s

# target to generate assembly for a file
vendor/common/flash_fw_check.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/flash_fw_check.c.s
.PHONY : vendor/common/flash_fw_check.c.s

vendor/common/flash_prot.obj: vendor/common/flash_prot.c.obj
.PHONY : vendor/common/flash_prot.obj

# target to build an object file
vendor/common/flash_prot.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/flash_prot.c.obj
.PHONY : vendor/common/flash_prot.c.obj

vendor/common/flash_prot.i: vendor/common/flash_prot.c.i
.PHONY : vendor/common/flash_prot.i

# target to preprocess a source file
vendor/common/flash_prot.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/flash_prot.c.i
.PHONY : vendor/common/flash_prot.c.i

vendor/common/flash_prot.s: vendor/common/flash_prot.c.s
.PHONY : vendor/common/flash_prot.s

# target to generate assembly for a file
vendor/common/flash_prot.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/flash_prot.c.s
.PHONY : vendor/common/flash_prot.c.s

vendor/common/simple_sdp.obj: vendor/common/simple_sdp.c.obj
.PHONY : vendor/common/simple_sdp.obj

# target to build an object file
vendor/common/simple_sdp.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/simple_sdp.c.obj
.PHONY : vendor/common/simple_sdp.c.obj

vendor/common/simple_sdp.i: vendor/common/simple_sdp.c.i
.PHONY : vendor/common/simple_sdp.i

# target to preprocess a source file
vendor/common/simple_sdp.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/simple_sdp.c.i
.PHONY : vendor/common/simple_sdp.c.i

vendor/common/simple_sdp.s: vendor/common/simple_sdp.c.s
.PHONY : vendor/common/simple_sdp.s

# target to generate assembly for a file
vendor/common/simple_sdp.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/simple_sdp.c.s
.PHONY : vendor/common/simple_sdp.c.s

vendor/common/tlkapi_debug.obj: vendor/common/tlkapi_debug.c.obj
.PHONY : vendor/common/tlkapi_debug.obj

# target to build an object file
vendor/common/tlkapi_debug.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/tlkapi_debug.c.obj
.PHONY : vendor/common/tlkapi_debug.c.obj

vendor/common/tlkapi_debug.i: vendor/common/tlkapi_debug.c.i
.PHONY : vendor/common/tlkapi_debug.i

# target to preprocess a source file
vendor/common/tlkapi_debug.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/tlkapi_debug.c.i
.PHONY : vendor/common/tlkapi_debug.c.i

vendor/common/tlkapi_debug.s: vendor/common/tlkapi_debug.c.s
.PHONY : vendor/common/tlkapi_debug.s

# target to generate assembly for a file
vendor/common/tlkapi_debug.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/tlkapi_debug.c.s
.PHONY : vendor/common/tlkapi_debug.c.s

vendor/common/user_config.obj: vendor/common/user_config.c.obj
.PHONY : vendor/common/user_config.obj

# target to build an object file
vendor/common/user_config.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/user_config.c.obj
.PHONY : vendor/common/user_config.c.obj

vendor/common/user_config.i: vendor/common/user_config.c.i
.PHONY : vendor/common/user_config.i

# target to preprocess a source file
vendor/common/user_config.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/user_config.c.i
.PHONY : vendor/common/user_config.c.i

vendor/common/user_config.s: vendor/common/user_config.c.s
.PHONY : vendor/common/user_config.s

# target to generate assembly for a file
vendor/common/user_config.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/common/user_config.c.s
.PHONY : vendor/common/user_config.c.s

vendor/user_app/agreement/agreement.obj: vendor/user_app/agreement/agreement.c.obj
.PHONY : vendor/user_app/agreement/agreement.obj

# target to build an object file
vendor/user_app/agreement/agreement.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/agreement.c.obj
.PHONY : vendor/user_app/agreement/agreement.c.obj

vendor/user_app/agreement/agreement.i: vendor/user_app/agreement/agreement.c.i
.PHONY : vendor/user_app/agreement/agreement.i

# target to preprocess a source file
vendor/user_app/agreement/agreement.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/agreement.c.i
.PHONY : vendor/user_app/agreement/agreement.c.i

vendor/user_app/agreement/agreement.s: vendor/user_app/agreement/agreement.c.s
.PHONY : vendor/user_app/agreement/agreement.s

# target to generate assembly for a file
vendor/user_app/agreement/agreement.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/agreement.c.s
.PHONY : vendor/user_app/agreement/agreement.c.s

vendor/user_app/agreement/rzn/rzn_agreement.obj: vendor/user_app/agreement/rzn/rzn_agreement.c.obj
.PHONY : vendor/user_app/agreement/rzn/rzn_agreement.obj

# target to build an object file
vendor/user_app/agreement/rzn/rzn_agreement.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/rzn/rzn_agreement.c.obj
.PHONY : vendor/user_app/agreement/rzn/rzn_agreement.c.obj

vendor/user_app/agreement/rzn/rzn_agreement.i: vendor/user_app/agreement/rzn/rzn_agreement.c.i
.PHONY : vendor/user_app/agreement/rzn/rzn_agreement.i

# target to preprocess a source file
vendor/user_app/agreement/rzn/rzn_agreement.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/rzn/rzn_agreement.c.i
.PHONY : vendor/user_app/agreement/rzn/rzn_agreement.c.i

vendor/user_app/agreement/rzn/rzn_agreement.s: vendor/user_app/agreement/rzn/rzn_agreement.c.s
.PHONY : vendor/user_app/agreement/rzn/rzn_agreement.s

# target to generate assembly for a file
vendor/user_app/agreement/rzn/rzn_agreement.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/rzn/rzn_agreement.c.s
.PHONY : vendor/user_app/agreement/rzn/rzn_agreement.c.s

vendor/user_app/at_cmd/app_at_cmd.obj: vendor/user_app/at_cmd/app_at_cmd.c.obj
.PHONY : vendor/user_app/at_cmd/app_at_cmd.obj

# target to build an object file
vendor/user_app/at_cmd/app_at_cmd.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/at_cmd/app_at_cmd.c.obj
.PHONY : vendor/user_app/at_cmd/app_at_cmd.c.obj

vendor/user_app/at_cmd/app_at_cmd.i: vendor/user_app/at_cmd/app_at_cmd.c.i
.PHONY : vendor/user_app/at_cmd/app_at_cmd.i

# target to preprocess a source file
vendor/user_app/at_cmd/app_at_cmd.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/at_cmd/app_at_cmd.c.i
.PHONY : vendor/user_app/at_cmd/app_at_cmd.c.i

vendor/user_app/at_cmd/app_at_cmd.s: vendor/user_app/at_cmd/app_at_cmd.c.s
.PHONY : vendor/user_app/at_cmd/app_at_cmd.s

# target to generate assembly for a file
vendor/user_app/at_cmd/app_at_cmd.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/at_cmd/app_at_cmd.c.s
.PHONY : vendor/user_app/at_cmd/app_at_cmd.c.s

vendor/user_app/att_ble/app_ble.obj: vendor/user_app/att_ble/app_ble.c.obj
.PHONY : vendor/user_app/att_ble/app_ble.obj

# target to build an object file
vendor/user_app/att_ble/app_ble.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/att_ble/app_ble.c.obj
.PHONY : vendor/user_app/att_ble/app_ble.c.obj

vendor/user_app/att_ble/app_ble.i: vendor/user_app/att_ble/app_ble.c.i
.PHONY : vendor/user_app/att_ble/app_ble.i

# target to preprocess a source file
vendor/user_app/att_ble/app_ble.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/att_ble/app_ble.c.i
.PHONY : vendor/user_app/att_ble/app_ble.c.i

vendor/user_app/att_ble/app_ble.s: vendor/user_app/att_ble/app_ble.c.s
.PHONY : vendor/user_app/att_ble/app_ble.s

# target to generate assembly for a file
vendor/user_app/att_ble/app_ble.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/att_ble/app_ble.c.s
.PHONY : vendor/user_app/att_ble/app_ble.c.s

vendor/user_app/bms/bms_data.obj: vendor/user_app/bms/bms_data.c.obj
.PHONY : vendor/user_app/bms/bms_data.obj

# target to build an object file
vendor/user_app/bms/bms_data.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data.c.obj
.PHONY : vendor/user_app/bms/bms_data.c.obj

vendor/user_app/bms/bms_data.i: vendor/user_app/bms/bms_data.c.i
.PHONY : vendor/user_app/bms/bms_data.i

# target to preprocess a source file
vendor/user_app/bms/bms_data.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data.c.i
.PHONY : vendor/user_app/bms/bms_data.c.i

vendor/user_app/bms/bms_data.s: vendor/user_app/bms/bms_data.c.s
.PHONY : vendor/user_app/bms/bms_data.s

# target to generate assembly for a file
vendor/user_app/bms/bms_data.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data.c.s
.PHONY : vendor/user_app/bms/bms_data.c.s

vendor/user_app/bms/bms_data_tool.obj: vendor/user_app/bms/bms_data_tool.c.obj
.PHONY : vendor/user_app/bms/bms_data_tool.obj

# target to build an object file
vendor/user_app/bms/bms_data_tool.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data_tool.c.obj
.PHONY : vendor/user_app/bms/bms_data_tool.c.obj

vendor/user_app/bms/bms_data_tool.i: vendor/user_app/bms/bms_data_tool.c.i
.PHONY : vendor/user_app/bms/bms_data_tool.i

# target to preprocess a source file
vendor/user_app/bms/bms_data_tool.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data_tool.c.i
.PHONY : vendor/user_app/bms/bms_data_tool.c.i

vendor/user_app/bms/bms_data_tool.s: vendor/user_app/bms/bms_data_tool.c.s
.PHONY : vendor/user_app/bms/bms_data_tool.s

# target to generate assembly for a file
vendor/user_app/bms/bms_data_tool.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data_tool.c.s
.PHONY : vendor/user_app/bms/bms_data_tool.c.s

vendor/user_app/bms/zy/sh367601xb.obj: vendor/user_app/bms/zy/sh367601xb.c.obj
.PHONY : vendor/user_app/bms/zy/sh367601xb.obj

# target to build an object file
vendor/user_app/bms/zy/sh367601xb.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb.c.obj
.PHONY : vendor/user_app/bms/zy/sh367601xb.c.obj

vendor/user_app/bms/zy/sh367601xb.i: vendor/user_app/bms/zy/sh367601xb.c.i
.PHONY : vendor/user_app/bms/zy/sh367601xb.i

# target to preprocess a source file
vendor/user_app/bms/zy/sh367601xb.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb.c.i
.PHONY : vendor/user_app/bms/zy/sh367601xb.c.i

vendor/user_app/bms/zy/sh367601xb.s: vendor/user_app/bms/zy/sh367601xb.c.s
.PHONY : vendor/user_app/bms/zy/sh367601xb.s

# target to generate assembly for a file
vendor/user_app/bms/zy/sh367601xb.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb.c.s
.PHONY : vendor/user_app/bms/zy/sh367601xb.c.s

vendor/user_app/bms/zy/sh367601xb_chip.obj: vendor/user_app/bms/zy/sh367601xb_chip.c.obj
.PHONY : vendor/user_app/bms/zy/sh367601xb_chip.obj

# target to build an object file
vendor/user_app/bms/zy/sh367601xb_chip.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_chip.c.obj
.PHONY : vendor/user_app/bms/zy/sh367601xb_chip.c.obj

vendor/user_app/bms/zy/sh367601xb_chip.i: vendor/user_app/bms/zy/sh367601xb_chip.c.i
.PHONY : vendor/user_app/bms/zy/sh367601xb_chip.i

# target to preprocess a source file
vendor/user_app/bms/zy/sh367601xb_chip.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_chip.c.i
.PHONY : vendor/user_app/bms/zy/sh367601xb_chip.c.i

vendor/user_app/bms/zy/sh367601xb_chip.s: vendor/user_app/bms/zy/sh367601xb_chip.c.s
.PHONY : vendor/user_app/bms/zy/sh367601xb_chip.s

# target to generate assembly for a file
vendor/user_app/bms/zy/sh367601xb_chip.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_chip.c.s
.PHONY : vendor/user_app/bms/zy/sh367601xb_chip.c.s

vendor/user_app/bms/zy/sh367601xb_communication.obj: vendor/user_app/bms/zy/sh367601xb_communication.c.obj
.PHONY : vendor/user_app/bms/zy/sh367601xb_communication.obj

# target to build an object file
vendor/user_app/bms/zy/sh367601xb_communication.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_communication.c.obj
.PHONY : vendor/user_app/bms/zy/sh367601xb_communication.c.obj

vendor/user_app/bms/zy/sh367601xb_communication.i: vendor/user_app/bms/zy/sh367601xb_communication.c.i
.PHONY : vendor/user_app/bms/zy/sh367601xb_communication.i

# target to preprocess a source file
vendor/user_app/bms/zy/sh367601xb_communication.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_communication.c.i
.PHONY : vendor/user_app/bms/zy/sh367601xb_communication.c.i

vendor/user_app/bms/zy/sh367601xb_communication.s: vendor/user_app/bms/zy/sh367601xb_communication.c.s
.PHONY : vendor/user_app/bms/zy/sh367601xb_communication.s

# target to generate assembly for a file
vendor/user_app/bms/zy/sh367601xb_communication.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_communication.c.s
.PHONY : vendor/user_app/bms/zy/sh367601xb_communication.c.s

vendor/user_app/bms/zy/sh367601xb_config.obj: vendor/user_app/bms/zy/sh367601xb_config.c.obj
.PHONY : vendor/user_app/bms/zy/sh367601xb_config.obj

# target to build an object file
vendor/user_app/bms/zy/sh367601xb_config.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_config.c.obj
.PHONY : vendor/user_app/bms/zy/sh367601xb_config.c.obj

vendor/user_app/bms/zy/sh367601xb_config.i: vendor/user_app/bms/zy/sh367601xb_config.c.i
.PHONY : vendor/user_app/bms/zy/sh367601xb_config.i

# target to preprocess a source file
vendor/user_app/bms/zy/sh367601xb_config.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_config.c.i
.PHONY : vendor/user_app/bms/zy/sh367601xb_config.c.i

vendor/user_app/bms/zy/sh367601xb_config.s: vendor/user_app/bms/zy/sh367601xb_config.c.s
.PHONY : vendor/user_app/bms/zy/sh367601xb_config.s

# target to generate assembly for a file
vendor/user_app/bms/zy/sh367601xb_config.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_config.c.s
.PHONY : vendor/user_app/bms/zy/sh367601xb_config.c.s

vendor/user_app/bms/zy/sh367601xb_converter.obj: vendor/user_app/bms/zy/sh367601xb_converter.c.obj
.PHONY : vendor/user_app/bms/zy/sh367601xb_converter.obj

# target to build an object file
vendor/user_app/bms/zy/sh367601xb_converter.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_converter.c.obj
.PHONY : vendor/user_app/bms/zy/sh367601xb_converter.c.obj

vendor/user_app/bms/zy/sh367601xb_converter.i: vendor/user_app/bms/zy/sh367601xb_converter.c.i
.PHONY : vendor/user_app/bms/zy/sh367601xb_converter.i

# target to preprocess a source file
vendor/user_app/bms/zy/sh367601xb_converter.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_converter.c.i
.PHONY : vendor/user_app/bms/zy/sh367601xb_converter.c.i

vendor/user_app/bms/zy/sh367601xb_converter.s: vendor/user_app/bms/zy/sh367601xb_converter.c.s
.PHONY : vendor/user_app/bms/zy/sh367601xb_converter.s

# target to generate assembly for a file
vendor/user_app/bms/zy/sh367601xb_converter.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_converter.c.s
.PHONY : vendor/user_app/bms/zy/sh367601xb_converter.c.s

vendor/user_app/bms/zy/sh367601xb_parser.obj: vendor/user_app/bms/zy/sh367601xb_parser.c.obj
.PHONY : vendor/user_app/bms/zy/sh367601xb_parser.obj

# target to build an object file
vendor/user_app/bms/zy/sh367601xb_parser.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_parser.c.obj
.PHONY : vendor/user_app/bms/zy/sh367601xb_parser.c.obj

vendor/user_app/bms/zy/sh367601xb_parser.i: vendor/user_app/bms/zy/sh367601xb_parser.c.i
.PHONY : vendor/user_app/bms/zy/sh367601xb_parser.i

# target to preprocess a source file
vendor/user_app/bms/zy/sh367601xb_parser.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_parser.c.i
.PHONY : vendor/user_app/bms/zy/sh367601xb_parser.c.i

vendor/user_app/bms/zy/sh367601xb_parser.s: vendor/user_app/bms/zy/sh367601xb_parser.c.s
.PHONY : vendor/user_app/bms/zy/sh367601xb_parser.s

# target to generate assembly for a file
vendor/user_app/bms/zy/sh367601xb_parser.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_parser.c.s
.PHONY : vendor/user_app/bms/zy/sh367601xb_parser.c.s

vendor/user_app/bms/zy/sh367601xb_tool.obj: vendor/user_app/bms/zy/sh367601xb_tool.c.obj
.PHONY : vendor/user_app/bms/zy/sh367601xb_tool.obj

# target to build an object file
vendor/user_app/bms/zy/sh367601xb_tool.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_tool.c.obj
.PHONY : vendor/user_app/bms/zy/sh367601xb_tool.c.obj

vendor/user_app/bms/zy/sh367601xb_tool.i: vendor/user_app/bms/zy/sh367601xb_tool.c.i
.PHONY : vendor/user_app/bms/zy/sh367601xb_tool.i

# target to preprocess a source file
vendor/user_app/bms/zy/sh367601xb_tool.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_tool.c.i
.PHONY : vendor/user_app/bms/zy/sh367601xb_tool.c.i

vendor/user_app/bms/zy/sh367601xb_tool.s: vendor/user_app/bms/zy/sh367601xb_tool.c.s
.PHONY : vendor/user_app/bms/zy/sh367601xb_tool.s

# target to generate assembly for a file
vendor/user_app/bms/zy/sh367601xb_tool.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_tool.c.s
.PHONY : vendor/user_app/bms/zy/sh367601xb_tool.c.s

vendor/user_app/flash/user_app_flash.obj: vendor/user_app/flash/user_app_flash.c.obj
.PHONY : vendor/user_app/flash/user_app_flash.obj

# target to build an object file
vendor/user_app/flash/user_app_flash.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/flash/user_app_flash.c.obj
.PHONY : vendor/user_app/flash/user_app_flash.c.obj

vendor/user_app/flash/user_app_flash.i: vendor/user_app/flash/user_app_flash.c.i
.PHONY : vendor/user_app/flash/user_app_flash.i

# target to preprocess a source file
vendor/user_app/flash/user_app_flash.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/flash/user_app_flash.c.i
.PHONY : vendor/user_app/flash/user_app_flash.c.i

vendor/user_app/flash/user_app_flash.s: vendor/user_app/flash/user_app_flash.c.s
.PHONY : vendor/user_app/flash/user_app_flash.s

# target to generate assembly for a file
vendor/user_app/flash/user_app_flash.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/flash/user_app_flash.c.s
.PHONY : vendor/user_app/flash/user_app_flash.c.s

vendor/user_app/list/queue/queue.obj: vendor/user_app/list/queue/queue.c.obj
.PHONY : vendor/user_app/list/queue/queue.obj

# target to build an object file
vendor/user_app/list/queue/queue.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/list/queue/queue.c.obj
.PHONY : vendor/user_app/list/queue/queue.c.obj

vendor/user_app/list/queue/queue.i: vendor/user_app/list/queue/queue.c.i
.PHONY : vendor/user_app/list/queue/queue.i

# target to preprocess a source file
vendor/user_app/list/queue/queue.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/list/queue/queue.c.i
.PHONY : vendor/user_app/list/queue/queue.c.i

vendor/user_app/list/queue/queue.s: vendor/user_app/list/queue/queue.c.s
.PHONY : vendor/user_app/list/queue/queue.s

# target to generate assembly for a file
vendor/user_app/list/queue/queue.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/list/queue/queue.c.s
.PHONY : vendor/user_app/list/queue/queue.c.s

vendor/user_app/system_main/app_mian.obj: vendor/user_app/system_main/app_mian.c.obj
.PHONY : vendor/user_app/system_main/app_mian.obj

# target to build an object file
vendor/user_app/system_main/app_mian.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/system_main/app_mian.c.obj
.PHONY : vendor/user_app/system_main/app_mian.c.obj

vendor/user_app/system_main/app_mian.i: vendor/user_app/system_main/app_mian.c.i
.PHONY : vendor/user_app/system_main/app_mian.i

# target to preprocess a source file
vendor/user_app/system_main/app_mian.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/system_main/app_mian.c.i
.PHONY : vendor/user_app/system_main/app_mian.c.i

vendor/user_app/system_main/app_mian.s: vendor/user_app/system_main/app_mian.c.s
.PHONY : vendor/user_app/system_main/app_mian.s

# target to generate assembly for a file
vendor/user_app/system_main/app_mian.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/system_main/app_mian.c.s
.PHONY : vendor/user_app/system_main/app_mian.c.s

vendor/user_app/uart/app_usart.obj: vendor/user_app/uart/app_usart.c.obj
.PHONY : vendor/user_app/uart/app_usart.obj

# target to build an object file
vendor/user_app/uart/app_usart.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/uart/app_usart.c.obj
.PHONY : vendor/user_app/uart/app_usart.c.obj

vendor/user_app/uart/app_usart.i: vendor/user_app/uart/app_usart.c.i
.PHONY : vendor/user_app/uart/app_usart.i

# target to preprocess a source file
vendor/user_app/uart/app_usart.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/uart/app_usart.c.i
.PHONY : vendor/user_app/uart/app_usart.c.i

vendor/user_app/uart/app_usart.s: vendor/user_app/uart/app_usart.c.s
.PHONY : vendor/user_app/uart/app_usart.s

# target to generate assembly for a file
vendor/user_app/uart/app_usart.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/uart/app_usart.c.s
.PHONY : vendor/user_app/uart/app_usart.c.s

vendor/user_app/user_app_main.obj: vendor/user_app/user_app_main.c.obj
.PHONY : vendor/user_app/user_app_main.obj

# target to build an object file
vendor/user_app/user_app_main.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_main.c.obj
.PHONY : vendor/user_app/user_app_main.c.obj

vendor/user_app/user_app_main.i: vendor/user_app/user_app_main.c.i
.PHONY : vendor/user_app/user_app_main.i

# target to preprocess a source file
vendor/user_app/user_app_main.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_main.c.i
.PHONY : vendor/user_app/user_app_main.c.i

vendor/user_app/user_app_main.s: vendor/user_app/user_app_main.c.s
.PHONY : vendor/user_app/user_app_main.s

# target to generate assembly for a file
vendor/user_app/user_app_main.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\825x_ble_sample.dir\build.make CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_main.c.s
.PHONY : vendor/user_app/user_app_main.c.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... rebuild_cache
	@echo ... 825x_ble_sample
	@echo ... application/app/usbaud.obj
	@echo ... application/app/usbaud.i
	@echo ... application/app/usbaud.s
	@echo ... application/app/usbcdc.obj
	@echo ... application/app/usbcdc.i
	@echo ... application/app/usbcdc.s
	@echo ... application/app/usbkb.obj
	@echo ... application/app/usbkb.i
	@echo ... application/app/usbkb.s
	@echo ... application/app/usbmouse.obj
	@echo ... application/app/usbmouse.i
	@echo ... application/app/usbmouse.s
	@echo ... application/audio/adpcm.obj
	@echo ... application/audio/adpcm.i
	@echo ... application/audio/adpcm.s
	@echo ... application/audio/gl_audio.obj
	@echo ... application/audio/gl_audio.i
	@echo ... application/audio/gl_audio.s
	@echo ... application/audio/tl_audio.obj
	@echo ... application/audio/tl_audio.i
	@echo ... application/audio/tl_audio.s
	@echo ... application/keyboard/keyboard.obj
	@echo ... application/keyboard/keyboard.i
	@echo ... application/keyboard/keyboard.s
	@echo ... application/print/putchar.obj
	@echo ... application/print/putchar.i
	@echo ... application/print/putchar.s
	@echo ... application/print/u_printf.obj
	@echo ... application/print/u_printf.i
	@echo ... application/print/u_printf.s
	@echo ... application/usbstd/usb.obj
	@echo ... application/usbstd/usb.i
	@echo ... application/usbstd/usb.s
	@echo ... application/usbstd/usbdesc.obj
	@echo ... application/usbstd/usbdesc.i
	@echo ... application/usbstd/usbdesc.s
	@echo ... boot/B85/cstartup_825x.obj
	@echo ... common/sdk_version.obj
	@echo ... common/sdk_version.i
	@echo ... common/sdk_version.s
	@echo ... common/string.obj
	@echo ... common/string.i
	@echo ... common/string.s
	@echo ... common/utility.obj
	@echo ... common/utility.i
	@echo ... common/utility.s
	@echo ... div_mod.obj
	@echo ... drivers/8258/adc.obj
	@echo ... drivers/8258/adc.i
	@echo ... drivers/8258/adc.s
	@echo ... drivers/8258/aes.obj
	@echo ... drivers/8258/aes.i
	@echo ... drivers/8258/aes.s
	@echo ... drivers/8258/analog.obj
	@echo ... drivers/8258/analog.i
	@echo ... drivers/8258/analog.s
	@echo ... drivers/8258/audio.obj
	@echo ... drivers/8258/audio.i
	@echo ... drivers/8258/audio.s
	@echo ... drivers/8258/bsp.obj
	@echo ... drivers/8258/bsp.i
	@echo ... drivers/8258/bsp.s
	@echo ... drivers/8258/clock.obj
	@echo ... drivers/8258/clock.i
	@echo ... drivers/8258/clock.s
	@echo ... drivers/8258/driver_ext/ext_calibration.obj
	@echo ... drivers/8258/driver_ext/ext_calibration.i
	@echo ... drivers/8258/driver_ext/ext_calibration.s
	@echo ... drivers/8258/driver_ext/ext_misc.obj
	@echo ... drivers/8258/driver_ext/ext_misc.i
	@echo ... drivers/8258/driver_ext/ext_misc.s
	@echo ... drivers/8258/driver_ext/rf_pa.obj
	@echo ... drivers/8258/driver_ext/rf_pa.i
	@echo ... drivers/8258/driver_ext/rf_pa.s
	@echo ... drivers/8258/driver_ext/software_uart.obj
	@echo ... drivers/8258/driver_ext/software_uart.i
	@echo ... drivers/8258/driver_ext/software_uart.s
	@echo ... drivers/8258/emi.obj
	@echo ... drivers/8258/emi.i
	@echo ... drivers/8258/emi.s
	@echo ... drivers/8258/flash.obj
	@echo ... drivers/8258/flash.i
	@echo ... drivers/8258/flash.s
	@echo ... drivers/8258/flash/flash_mid011460c8.obj
	@echo ... drivers/8258/flash/flash_mid011460c8.i
	@echo ... drivers/8258/flash/flash_mid011460c8.s
	@echo ... drivers/8258/flash/flash_mid1060c8.obj
	@echo ... drivers/8258/flash/flash_mid1060c8.i
	@echo ... drivers/8258/flash/flash_mid1060c8.s
	@echo ... drivers/8258/flash/flash_mid13325e.obj
	@echo ... drivers/8258/flash/flash_mid13325e.i
	@echo ... drivers/8258/flash/flash_mid13325e.s
	@echo ... drivers/8258/flash/flash_mid134051.obj
	@echo ... drivers/8258/flash/flash_mid134051.i
	@echo ... drivers/8258/flash/flash_mid134051.s
	@echo ... drivers/8258/flash/flash_mid136085.obj
	@echo ... drivers/8258/flash/flash_mid136085.i
	@echo ... drivers/8258/flash/flash_mid136085.s
	@echo ... drivers/8258/flash/flash_mid1360c8.obj
	@echo ... drivers/8258/flash/flash_mid1360c8.i
	@echo ... drivers/8258/flash/flash_mid1360c8.s
	@echo ... drivers/8258/flash/flash_mid1360eb.obj
	@echo ... drivers/8258/flash/flash_mid1360eb.i
	@echo ... drivers/8258/flash/flash_mid1360eb.s
	@echo ... drivers/8258/flash/flash_mid14325e.obj
	@echo ... drivers/8258/flash/flash_mid14325e.i
	@echo ... drivers/8258/flash/flash_mid14325e.s
	@echo ... drivers/8258/flash/flash_mid1460c8.obj
	@echo ... drivers/8258/flash/flash_mid1460c8.i
	@echo ... drivers/8258/flash/flash_mid1460c8.s
	@echo ... drivers/8258/gpio.obj
	@echo ... drivers/8258/gpio.i
	@echo ... drivers/8258/gpio.s
	@echo ... drivers/8258/i2c.obj
	@echo ... drivers/8258/i2c.i
	@echo ... drivers/8258/i2c.s
	@echo ... drivers/8258/lpc.obj
	@echo ... drivers/8258/lpc.i
	@echo ... drivers/8258/lpc.s
	@echo ... drivers/8258/qdec.obj
	@echo ... drivers/8258/qdec.i
	@echo ... drivers/8258/qdec.s
	@echo ... drivers/8258/s7816.obj
	@echo ... drivers/8258/s7816.i
	@echo ... drivers/8258/s7816.s
	@echo ... drivers/8258/spi.obj
	@echo ... drivers/8258/spi.i
	@echo ... drivers/8258/spi.s
	@echo ... drivers/8258/timer.obj
	@echo ... drivers/8258/timer.i
	@echo ... drivers/8258/timer.s
	@echo ... drivers/8258/uart.obj
	@echo ... drivers/8258/uart.i
	@echo ... drivers/8258/uart.s
	@echo ... drivers/8258/usbhw.obj
	@echo ... drivers/8258/usbhw.i
	@echo ... drivers/8258/usbhw.s
	@echo ... drivers/8258/watchdog.obj
	@echo ... drivers/8258/watchdog.i
	@echo ... drivers/8258/watchdog.s
	@echo ... vendor/b85m_ble_sample/app.obj
	@echo ... vendor/b85m_ble_sample/app.i
	@echo ... vendor/b85m_ble_sample/app.s
	@echo ... vendor/b85m_ble_sample/app_att.obj
	@echo ... vendor/b85m_ble_sample/app_att.i
	@echo ... vendor/b85m_ble_sample/app_att.s
	@echo ... vendor/b85m_ble_sample/app_ui.obj
	@echo ... vendor/b85m_ble_sample/app_ui.i
	@echo ... vendor/b85m_ble_sample/app_ui.s
	@echo ... vendor/b85m_ble_sample/main.obj
	@echo ... vendor/b85m_ble_sample/main.i
	@echo ... vendor/b85m_ble_sample/main.s
	@echo ... vendor/common/app_buffer.obj
	@echo ... vendor/common/app_buffer.i
	@echo ... vendor/common/app_buffer.s
	@echo ... vendor/common/app_common.obj
	@echo ... vendor/common/app_common.i
	@echo ... vendor/common/app_common.s
	@echo ... vendor/common/battery_check.obj
	@echo ... vendor/common/battery_check.i
	@echo ... vendor/common/battery_check.s
	@echo ... vendor/common/ble_flash.obj
	@echo ... vendor/common/ble_flash.i
	@echo ... vendor/common/ble_flash.s
	@echo ... vendor/common/blt_fw_sign.obj
	@echo ... vendor/common/blt_fw_sign.i
	@echo ... vendor/common/blt_fw_sign.s
	@echo ... vendor/common/blt_led.obj
	@echo ... vendor/common/blt_led.i
	@echo ... vendor/common/blt_led.s
	@echo ... vendor/common/blt_soft_timer.obj
	@echo ... vendor/common/blt_soft_timer.i
	@echo ... vendor/common/blt_soft_timer.s
	@echo ... vendor/common/custom_pair.obj
	@echo ... vendor/common/custom_pair.i
	@echo ... vendor/common/custom_pair.s
	@echo ... vendor/common/flash_fw_check.obj
	@echo ... vendor/common/flash_fw_check.i
	@echo ... vendor/common/flash_fw_check.s
	@echo ... vendor/common/flash_prot.obj
	@echo ... vendor/common/flash_prot.i
	@echo ... vendor/common/flash_prot.s
	@echo ... vendor/common/simple_sdp.obj
	@echo ... vendor/common/simple_sdp.i
	@echo ... vendor/common/simple_sdp.s
	@echo ... vendor/common/tlkapi_debug.obj
	@echo ... vendor/common/tlkapi_debug.i
	@echo ... vendor/common/tlkapi_debug.s
	@echo ... vendor/common/user_config.obj
	@echo ... vendor/common/user_config.i
	@echo ... vendor/common/user_config.s
	@echo ... vendor/user_app/agreement/agreement.obj
	@echo ... vendor/user_app/agreement/agreement.i
	@echo ... vendor/user_app/agreement/agreement.s
	@echo ... vendor/user_app/agreement/rzn/rzn_agreement.obj
	@echo ... vendor/user_app/agreement/rzn/rzn_agreement.i
	@echo ... vendor/user_app/agreement/rzn/rzn_agreement.s
	@echo ... vendor/user_app/at_cmd/app_at_cmd.obj
	@echo ... vendor/user_app/at_cmd/app_at_cmd.i
	@echo ... vendor/user_app/at_cmd/app_at_cmd.s
	@echo ... vendor/user_app/att_ble/app_ble.obj
	@echo ... vendor/user_app/att_ble/app_ble.i
	@echo ... vendor/user_app/att_ble/app_ble.s
	@echo ... vendor/user_app/bms/bms_data.obj
	@echo ... vendor/user_app/bms/bms_data.i
	@echo ... vendor/user_app/bms/bms_data.s
	@echo ... vendor/user_app/bms/bms_data_tool.obj
	@echo ... vendor/user_app/bms/bms_data_tool.i
	@echo ... vendor/user_app/bms/bms_data_tool.s
	@echo ... vendor/user_app/bms/zy/sh367601xb.obj
	@echo ... vendor/user_app/bms/zy/sh367601xb.i
	@echo ... vendor/user_app/bms/zy/sh367601xb.s
	@echo ... vendor/user_app/bms/zy/sh367601xb_chip.obj
	@echo ... vendor/user_app/bms/zy/sh367601xb_chip.i
	@echo ... vendor/user_app/bms/zy/sh367601xb_chip.s
	@echo ... vendor/user_app/bms/zy/sh367601xb_communication.obj
	@echo ... vendor/user_app/bms/zy/sh367601xb_communication.i
	@echo ... vendor/user_app/bms/zy/sh367601xb_communication.s
	@echo ... vendor/user_app/bms/zy/sh367601xb_config.obj
	@echo ... vendor/user_app/bms/zy/sh367601xb_config.i
	@echo ... vendor/user_app/bms/zy/sh367601xb_config.s
	@echo ... vendor/user_app/bms/zy/sh367601xb_converter.obj
	@echo ... vendor/user_app/bms/zy/sh367601xb_converter.i
	@echo ... vendor/user_app/bms/zy/sh367601xb_converter.s
	@echo ... vendor/user_app/bms/zy/sh367601xb_parser.obj
	@echo ... vendor/user_app/bms/zy/sh367601xb_parser.i
	@echo ... vendor/user_app/bms/zy/sh367601xb_parser.s
	@echo ... vendor/user_app/bms/zy/sh367601xb_tool.obj
	@echo ... vendor/user_app/bms/zy/sh367601xb_tool.i
	@echo ... vendor/user_app/bms/zy/sh367601xb_tool.s
	@echo ... vendor/user_app/flash/user_app_flash.obj
	@echo ... vendor/user_app/flash/user_app_flash.i
	@echo ... vendor/user_app/flash/user_app_flash.s
	@echo ... vendor/user_app/list/queue/queue.obj
	@echo ... vendor/user_app/list/queue/queue.i
	@echo ... vendor/user_app/list/queue/queue.s
	@echo ... vendor/user_app/system_main/app_mian.obj
	@echo ... vendor/user_app/system_main/app_mian.i
	@echo ... vendor/user_app/system_main/app_mian.s
	@echo ... vendor/user_app/uart/app_usart.obj
	@echo ... vendor/user_app/uart/app_usart.i
	@echo ... vendor/user_app/uart/app_usart.s
	@echo ... vendor/user_app/user_app_main.obj
	@echo ... vendor/user_app/user_app_main.i
	@echo ... vendor/user_app/user_app_main.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

