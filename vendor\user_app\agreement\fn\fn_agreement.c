#include "fn_agreement.h"
#include "../../user_app_main.h"
#include "../../bms/zy/sh367601xb.h"
#include "../../flash/user_app_flash.h"
#include "../../list/queue/queue.h"

#if FN_PROJECT_ENABLE
extern SH367601B_Device sh367601xb;
extern Queue q;
static unsigned short crc16(unsigned char *data, unsigned char len)
{
    unsigned short cs = 0;
    for (int i = 0; i < len; i++)
    {
        cs ^= data[i];
        for (int j = 0; j < 8; j++)
        {
            if ((cs & 1) == 1)
            {
                cs >>= 1;
                cs ^= 0xa001;
            }
            else
            {
                cs >>= 1;
            }
        }
    }
    return cs;
}

void bluetooth_protocol_process(unsigned char *data, unsigned short len)
{
    if (0xAA == data[0] && len >= 4)
    {
        switch (data[1])
        {
            /* 电压校准（写） */
            case 0x02:
            {
                // u8 cmd[5] = {0}, flg = USER_APP_FLASH_FLG, batt_num = sh367601xb.bms_system.voltage_mgr.data.cell_num[0] + sh367601xb.bms_system.voltage_mgr.data.cell_num[1];
                // for (unsigned short i = 0; i < batt_num; i++)
                //     sh367601xb.bms_system.voltage_mgr.data.calibration_voltage[i] = (data[3 + (i * 2)] << 8) | data[4 + (i * 2)];
                // My_Flash_Write(FLASH_ADDR_VOLTAGE, &data[3], 48);
                // My_Flash_Write(FLASH_ADDR_VOLTAGE_FLG, &flg, 1);
                // cmd[0] = 0xAA;
                // cmd[1] = data[1];
                // cmd[2] = 0x00;
                // cmd[3] = 0x00;
                // cmd[4] = 0xFF;
                // blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            /* 采样电阻（写） */
            case 0x03:
            {
                // u8 cmd[5] = {0}, flg = USER_APP_FLASH_FLG;
                // sh367601xb.bms_system.current_mgr.data.sampling_resistor = ((data[3] << 8) | data[4]) / (float)1000000.0;
                // My_Flash_Write(FLASH_ADDR_RESISTANCE, &data[3], 2);
                // My_Flash_Write(FLASH_ADDR_RESISTANCE_FLG, &flg, 1);
                // cmd[0] = 0xAA;
                // cmd[1] = data[1];
                // cmd[2] = 0x00;
                // cmd[3] = 0x00;
                // cmd[4] = 0xFF;
                // blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            /* 读取电压 */
            case 0x04:
            {
                // u8 cmd[52], batt_num = sh367601xb.bms_system.voltage_mgr.data.cell_num[0] + sh367601xb.bms_system.voltage_mgr.data.cell_num[1];;
                // memcpy(&cmd[3], (char *)&sh367601xb.bms_system.voltage_mgr.data.single_voltage[0], batt_num * 2);
                // for (unsigned short i = 0; i < batt_num; i++)
                // {
                //     cmd[3 + (i * 2)] = sh367601xb.bms_system.voltage_mgr.data.calibration_voltage[i] >> 8;
                //     cmd[4 + (i * 2)] = sh367601xb.bms_system.voltage_mgr.data.calibration_voltage[i];
                // }
                // cmd[0] = 0xAA;
                // cmd[1] = data[1];
                // cmd[2] = 0x30;
                // cmd[51] = 0xFF;
                // blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            /* 读取电流 */
            case 0x05:
            {
                // u8 cmd[6];
                // u16 sampling_r = sh367601xb.bms_system.current_mgr.data.sampling_resistor * 1000000;
                // sampling_r = BE16_TO_LE16(sampling_r);
                // memcpy(&cmd[3], &sampling_r, 2);
                // cmd[0] = 0xAA;
                // cmd[1] = data[1];
                // cmd[2] = 0x02;
                // cmd[5] = 0xFF;
                // blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            /* 电池容量（读） */
            case 0x10:
            {
                // u8 cmd[12];
                // u32 capacity = BE32_TO_LE32(sh367601xb.bms_system.custom_param_mgr.data.battery_total_capacity);
                // memcpy(&cmd[3], &capacity, 4);
                // cmd[0] = 0xAA;
                // cmd[1] = data[1];
                // cmd[2] = 0x04;
                // cmd[7] = 0x00;
                // cmd[8] = 0x00;
                // cmd[9] = 0x00;
                // cmd[10] = 0x00;
                // cmd[11] = 0xFF;
                // blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            /* 电池容量（写） */
            case 0x11:
            {
                // u8 cmd[5] = {0}, flg = USER_APP_FLASH_FLG;
                // u32 curr_capacity = (data[7] << 24) | (data[8] << 16) | (data[9] << 8) | data[10];
                // sh367601xb.bms_system.custom_param_mgr.data.battery_total_capacity = (data[3] << 24) | (data[4] << 16) | (data[5] << 8) | data[6];
                // My_Flash_Write(FLASH_ADDR_BATT_CAPACITY, &data[3], 8);
                // My_Flash_Write(FLASH_ADDR_BATT_CAPACITY_FLG, &flg, 1);
                // sh367601xb.bms_system.soc_mgr.data.soc = (float)((curr_capacity * 100.0) / sh367601xb.bms_system.custom_param_mgr.data.battery_total_capacity);
                // cmd[0] = 0xAA;
                // cmd[1] = data[1];
                // cmd[2] = 0x00;
                // cmd[3] = 0x00;
                // cmd[4] = 0xFF;
                // blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            /* 零点校准 */
            case 0x12:
            {
            	// u8 cmd[5];
                // sh367601xb.bms_system.current_mgr.data.zero_calibration_flag = true;
                // cmd[0] = 0xAA;
                // cmd[1] = data[1];
                // cmd[2] = 0x00;
                // cmd[3] = 0x00;
                // cmd[4] = 0xFF;
                // blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            /* 写rom产品信息 */
            case 0x13:
            {
                char cn = data[4] - 10;
                unsigned char cmd[6];
                u8 flg = USER_APP_FLASH_FLG;
                
                /* 更新结构体中的芯片数量和类型 */
                sh367601xb.chip.data.chip_count = data[5];
                sh367601xb.chip.data.chip_type[0] = (SH367601B_ChipType)data[6];
                
                sh367601xb.config.method.create_rom_copy(&sh367601xb);
                sh367601xb.config.method.set_id(&sh367601xb, data[3]);
                sh367601xb.config.method.set_cn(&sh367601xb, 0, sh367601xb.chip.data.chip_count == 1 ? data[4] : 10);
                /* 将配置写入EEPROM */
                sh367601xb.config.method.pack_rom_data(sh367601xb.write_buff, &sh367601xb.write_rom);
                sh367601xb.chip.method.start_batch_write_rom(&sh367601xb, &q);
                
                cmd[0] = 0xAA;
                cmd[1] = data[1];
                cmd[2] = 0x02;
                cmd[3] = 0x00;
                cmd[4] = 0x00;
                cmd[5] = 0xFF;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            /* 写rom系统配置 */
            case 0x14:
            {
                unsigned char cmd[6];
                sh367601xb.config.method.create_rom_copy(&sh367601xb);
                sh367601xb.config.method.set_enmos(&sh367601xb, data[3]);
                sh367601xb.config.method.set_enmosr(&sh367601xb, data[4]);
                sh367601xb.config.method.set_chys(&sh367601xb, data[5]);
                sh367601xb.config.method.set_bals(&sh367601xb, data[6]);
                sh367601xb.config.method.set_chs(&sh367601xb, data[7]);
                sh367601xb.config.method.set_ocra(&sh367601xb, data[8]);
                sh367601xb.config.method.set_eovr(&sh367601xb, data[9]);
                sh367601xb.config.method.set_euvr(&sh367601xb, data[10]);
                sh367601xb.config.method.set_eow(&sh367601xb, data[11]);
                sh367601xb.config.method.set_eot3(&sh367601xb, data[12]);
                /* 将配置写入EEPROM */
                sh367601xb.config.method.pack_rom_data(sh367601xb.write_buff, &sh367601xb.write_rom);
                sh367601xb.chip.method.start_batch_write_rom(&sh367601xb, &q);
                
                cmd[0] = 0xAA;
                cmd[1] = data[1];
                cmd[2] = 0x02;
                cmd[3] = 0x00;
                cmd[4] = 0x00;
                cmd[5] = 0xFF;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            /* 写rom电压保护 */
            case 0x15:
            {
                unsigned char cmd[6];
                sh367601xb.config.method.create_rom_copy(&sh367601xb);
                sh367601xb.config.method.set_ov(&sh367601xb, (data[3] << 8) | data[4]);
                sh367601xb.config.method.set_ovt(&sh367601xb, data[5]);
                sh367601xb.config.method.set_ovr(&sh367601xb, (data[6] << 8) | data[7]);
                sh367601xb.config.method.set_balv(&sh367601xb, (data[8] << 8) | data[9]);
                sh367601xb.config.method.set_bald(&sh367601xb, data[10]);
                sh367601xb.config.method.set_balt(&sh367601xb, data[11]);
                sh367601xb.config.method.set_uvr(&sh367601xb, (data[12] << 8) | data[13]);
                sh367601xb.config.method.set_uv(&sh367601xb, (data[14] << 8) | data[15]);
                sh367601xb.config.method.set_uvt(&sh367601xb, data[16]);
                sh367601xb.config.method.set_lov(&sh367601xb, (data[17] << 8) | data[18]);
                /* 将配置写入EEPROM */
                sh367601xb.config.method.pack_rom_data(sh367601xb.write_buff, &sh367601xb.write_rom);
                sh367601xb.chip.method.start_batch_write_rom(&sh367601xb, &q);
                
                cmd[0] = 0xAA;
                cmd[1] = data[1];
                cmd[2] = 0x02;
                cmd[3] = 0x00;
                cmd[4] = 0x00;
                cmd[5] = 0xFF;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            /* 写rom电流保护 */
            case 0x16:
            {
                unsigned short ocd1v, occv;
                unsigned char cmd[6];
                sh367601xb.config.method.create_rom_copy(&sh367601xb);
                ocd1v = (data[3] << 8) | data[4];
                occv = (data[11] << 8) | data[12];
                sh367601xb.write_rom.ocd1v = ocd1v;
                sh367601xb.write_rom.ocd1t = data[5];
                sh367601xb.write_rom.ocd2v = data[6];
                sh367601xb.write_rom.ocd2t = data[7];
                sh367601xb.write_rom.sct = data[10];
                sh367601xb.write_rom.occv = occv;
                sh367601xb.write_rom.occt = data[13];
                /* 将配置写入EEPROM */
                sh367601xb.config.method.pack_rom_data(sh367601xb.write_buff, &sh367601xb.write_rom);
                sh367601xb.chip.method.start_batch_write_rom(&sh367601xb, &q);
                
                cmd[0] = 0xAA;
                cmd[1] = data[1];
                cmd[2] = 0x02;
                cmd[3] = 0x00;
                cmd[4] = 0x00;
                cmd[5] = 0xFF;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            /* 写rom温度保护 */
            case 0x17:
            {
                u8 cmd[6];
                sh367601xb.config.method.create_rom_copy(&sh367601xb);
                sh367601xb.write_rom.otc = data[3];
                sh367601xb.write_rom.otcr = data[4];
                sh367601xb.write_rom.utc = data[5];
                sh367601xb.write_rom.utcr = data[6];
                sh367601xb.write_rom.otd = data[7];
                sh367601xb.write_rom.otdr = data[8];
                sh367601xb.write_rom.utd = data[9];
                sh367601xb.write_rom.utdr = data[10];
                sh367601xb.config.method.set_tc(&sh367601xb, data[11]);
                /* 将配置写入EEPROM */
                sh367601xb.config.method.pack_rom_data(sh367601xb.write_buff, &sh367601xb.write_rom);
                sh367601xb.chip.method.start_batch_write_rom(&sh367601xb, &q);
                
                cmd[0] = 0xAA;
                cmd[1] = data[1];
                cmd[2] = 0x02;
                cmd[3] = 0x00;
                cmd[4] = 0x00;
                cmd[5] = 0xFF;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            /* 读rom产品信息 */ 
            case 0x18:
            {
                u8 cmd[8];  /* 扩展为8字节，增加芯片数量和类型 */
                cmd[0] = 0xAA;
                cmd[1] = data[1];
                cmd[2] = 0x04;  /* 数据长度增加到4 */
                cmd[7] = 0xFF;  /* 结束标志移到第7位 */
                cmd[3] = sh367601xb.rom[0].id;
                cmd[4] = sh367601xb.bms_system.voltage_mgr.data.battery_count;
                cmd[5] = sh367601xb.chip.data.chip_count;  /* 芯片数量 */
                cmd[6] = (unsigned char)sh367601xb.chip.data.chip_type[0];   /* 芯片类型 */
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            /* 读rom系统配置 */
            case 0x19:
            {
                u8 cmd[14];
                cmd[0] =  0xAA;
                cmd[1] =  data[1];
                cmd[2] =  0x0A;
                cmd[13] = 0xFF;
                cmd[3] =  sh367601xb.rom[0].enmos;
                cmd[4] =  sh367601xb.rom[0].enmosr;
                cmd[5] =  sh367601xb.rom[0].chys;
                cmd[6] =  sh367601xb.rom[0].bals;
                cmd[7] =  sh367601xb.rom[0].chs;
                cmd[8] =  sh367601xb.rom[0].ocra;
                cmd[9] =  sh367601xb.rom[0].eovr;
                cmd[10] = sh367601xb.rom[0].euvr;
                cmd[11] = sh367601xb.rom[0].eow;
                cmd[12] = sh367601xb.rom[0].eot3;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            /* 读rom电压保护 */
            case 0x20:
            {
                u8 cmd[20];
                cmd[0]  = 0xAA;
                cmd[1]  = data[1];
                cmd[2]  = 0x10;
                cmd[19] = 0xFF;
                cmd[3] = sh367601xb.bms_system.protection_param_mgr.data.overvoltage_protection >> 8;
                cmd[4] = sh367601xb.bms_system.protection_param_mgr.data.overvoltage_protection;
                cmd[5] = sh367601xb.rom[0].ovt;
                cmd[6] = sh367601xb.bms_system.protection_param_mgr.data.overvoltage_recovery >> 8;
                cmd[7] = sh367601xb.bms_system.protection_param_mgr.data.overvoltage_recovery;
                cmd[8] = sh367601xb.bms_system.protection_param_mgr.data.balance_start_voltage >> 8;
                cmd[9] = sh367601xb.bms_system.protection_param_mgr.data.balance_start_voltage;
                cmd[10] = sh367601xb.rom[0].bald;
                cmd[11] = sh367601xb.rom[0].balt;
                cmd[12] = sh367601xb.bms_system.protection_param_mgr.data.undervoltage_recovery >> 8;
                cmd[13] = sh367601xb.bms_system.protection_param_mgr.data.undervoltage_recovery;
                cmd[14] = sh367601xb.bms_system.protection_param_mgr.data.undervoltage_protection >> 8;
                cmd[15] = sh367601xb.bms_system.protection_param_mgr.data.undervoltage_protection;
                cmd[16] = sh367601xb.rom[0].uvt;
                u16 temp;
                temp = sh367601xb.converter.method.lov_to_voltage(sh367601xb.rom[0].lov);
                cmd[17] = temp >> 8;
                cmd[18] = temp;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            /* 读rom电流保护 */
            case 0x21:
            {
                u8 cmd[15];
                u16 temp;
                cmd[0]  = 0xAA;
                cmd[1]  = data[1];
                cmd[2]  = 0x0C;
                cmd[14] = 0xFF;
                temp = sh367601xb.bms_system.protection_param_mgr.data.discharge_overcurrent1;
                cmd[3] = sh367601xb.bms_system.protection_param_mgr.data.discharge_overcurrent1 >> 8;
                cmd[4] = sh367601xb.bms_system.protection_param_mgr.data.discharge_overcurrent1;
                cmd[5] = sh367601xb.rom[0].ocd1t;
                cmd[6] = sh367601xb.rom[0].ocd2v;
                cmd[7] = sh367601xb.rom[0].ocd2t;
                temp = sh367601xb.bms_system.protection_param_mgr.data.discharge_overcurrent2 * 2;
                cmd[8] = temp >> 8;
                cmd[9] = temp;
                cmd[10] = sh367601xb.rom[0].sct;
                cmd[11] = sh367601xb.bms_system.protection_param_mgr.data.charge_overcurrent1 >> 8;
                cmd[12] = sh367601xb.bms_system.protection_param_mgr.data.charge_overcurrent1;
                cmd[13] = sh367601xb.rom[0].occt;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            /* 读rom温度保护 */
            case 0x22:
            {
                u8 cmd[13];
                cmd[0]  = 0xAA;
                cmd[1]  = data[1];
                cmd[2]  = 0x09;
                cmd[12] = 0xFF;
                cmd[3] = sh367601xb.bms_system.protection_param_mgr.data.charge_high_temp_protection;
                cmd[4]  = sh367601xb.bms_system.protection_param_mgr.data.charge_high_temp_recovery;
                cmd[5]  = sh367601xb.bms_system.protection_param_mgr.data.charge_low_temp_protection;
                cmd[6]  = sh367601xb.bms_system.protection_param_mgr.data.charge_low_temp_recovery;
                cmd[7]  = sh367601xb.bms_system.protection_param_mgr.data.discharge_high_temp_protection;
                cmd[8]  = sh367601xb.bms_system.protection_param_mgr.data.discharge_high_temp_recovery;
                cmd[9]  = sh367601xb.bms_system.protection_param_mgr.data.discharge_low_temp_protection;
                cmd[10] = sh367601xb.bms_system.protection_param_mgr.data.discharge_low_temp_recovery;
                cmd[11] = sh367601xb.converter.method.tc_to_delay(sh367601xb.rom[0].tc);
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            /* 一键唤醒 */
            case 0x23:      
            {
                u8 cmd[5] = {0};
                cmd[0] = 0xAA;
                cmd[1] = data[1];
                cmd[2] = 0x00;
                cmd[3] = 0x00;
                cmd[4] = 0xFF;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                /* 使用队列方法执行一键唤醒 */
                // queue_push(&q, QUEUE_RESET_TASK, QUEUE_RESET_TIME);
                // if (sh367601xb.chip.data.chip_count > 1)
                //     queue_push(&q, QUEUE_RESET_TASK, QUEUE_RESET_TIME);
                break;
            }
            /* 读取告警信息 */
            case 0x24:
            {
                u8 cmd[8] = {0};
                u32 status = 0;
                cmd[0] = 0xAA;
                cmd[1] = data[1];
                cmd[2] = 0x04;
                
                /* 组合所有告警状态 */
                status |= (sh367601xb.bms_system.alarm_mgr.data.charge_over_temp << 11);
                status |= (sh367601xb.bms_system.alarm_mgr.data.charge_overcurrent << 3);
                status |= (sh367601xb.bms_system.alarm_mgr.data.discharge_overcurrent1 << 4);
                status |= (sh367601xb.bms_system.alarm_mgr.data.discharge_overcurrent2 << 5);
                status |= (sh367601xb.bms_system.alarm_mgr.data.undervoltage << 6);
                status |= (sh367601xb.bms_system.alarm_mgr.data.overvoltage << 7);
                status |= (sh367601xb.bms_system.alarm_mgr.data.discharge_over_temp << 9);
                status |= (sh367601xb.bms_system.alarm_mgr.data.discharge_under_temp << 10);
                status |= (sh367601xb.bms_system.alarm_mgr.data.charge_under_temp << 12);
                status |= (sh367601xb.bms_system.status_mgr.data.balance_status << 13);
                status |= (sh367601xb.bms_system.status_mgr.data.charge_status << 17);
                status |= (sh367601xb.bms_system.status_mgr.data.discharge_status << 18);
                status |= (sh367601xb.bms_system.status_mgr.data.charge_mos << 19);
                status |= (sh367601xb.bms_system.status_mgr.data.discharge_mos << 20);

                cmd[3] = status >> 24;
                cmd[4] = status >> 16;
                cmd[5] = status >> 8;
                cmd[6] = status;
                cmd[7] = 0xFF;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            default: break;
        }
    }

    /* 读取实时信息 */
    if (0x7e == data[0] && data[2] <= 0x0A && len >= 4)
    {
        switch (data[2])
        {
            /* BMS实时信息 */
            case 0x01:
            {
                u8 cmd[65] = {0};
                
                cmd[0] = 0x7E;
                cmd[1] = 0x01;
                cmd[2] = 0x01;
                cmd[3] = 0x2F;
                /* 总电压 */
                cmd[4] = sh367601xb.bms_system.voltage_mgr.data.total_voltage >> 24;
                cmd[5] = sh367601xb.bms_system.voltage_mgr.data.total_voltage >> 16;
                cmd[6] = sh367601xb.bms_system.voltage_mgr.data.total_voltage >> 8;
                cmd[7] = sh367601xb.bms_system.voltage_mgr.data.total_voltage;
                /* 电流 */
                cmd[8] =  sh367601xb.bms_system.current_mgr.data.total_current >> 24;
                cmd[9] =  sh367601xb.bms_system.current_mgr.data.total_current >> 16;
                cmd[10] = sh367601xb.bms_system.current_mgr.data.total_current >> 8;
                cmd[11] = sh367601xb.bms_system.current_mgr.data.total_current;
                /* 电池健康度、电量 */
                cmd[12] = sh367601xb.bms_system.battery_state_mgr.data.soh / 100;
                cmd[13] = sh367601xb.bms_system.battery_state_mgr.data.soc / 100;
                /* 剩余容量、设计容量 */
                cmd[14] = 0x00;
                cmd[15] = 0x00;
                cmd[16] = 0x00;
                cmd[17] = 0x00;
                /* 最大电压、最小电压 */
                cmd[18] = sh367601xb.bms_system.voltage_mgr.data.max_voltage >> 8;
                cmd[19] = sh367601xb.bms_system.voltage_mgr.data.max_voltage;
                cmd[20] = sh367601xb.bms_system.voltage_mgr.data.min_voltage >> 8;
                cmd[21] = sh367601xb.bms_system.voltage_mgr.data.min_voltage;
                /* 最高电芯单点温度
                最低电芯单点温度
                最高MOS单点温度
                最高均衡单点温度
                最高环境单点温度
                最低环境单点温度 */
                cmd[22] = 0x00;
                cmd[23] = 0x00;
                cmd[24] = 0x00;
                cmd[25] = 0x00;
                cmd[26] = 0x00;
                cmd[27] = 0x00;
                /* 电池状态 */
                cmd[28] = sh367601xb.bms_system.current_mgr.data.current_state;
                /* mos管状态、告警信息、系统状态 */
                cmd[29] = 0x00;
                cmd[30] = 0x00;
                cmd[31] = 0x00;
                cmd[32] = 0x00;
                cmd[33] = 0x00;
                cmd[34] = 0x00;
                cmd[35] = 0x00;
                cmd[36] = 0x00;
                cmd[37] = 0x00;
                cmd[38] = 0x00;
                cmd[39] = 0x00;
                cmd[40] = 0x00;
                cmd[41] = 0x00;
                cmd[42] = 0x00;
                cmd[43] = 0x00;
                cmd[44] = 0x00;
                /* 循环次数、满充容量 */
                cmd[45] = 0x00;
                cmd[46] = 0x00;
                cmd[47] = 0x00;
                cmd[48] = 0x00;
                cmd[49] = 0x00;
                cmd[50] = 0x00;
                cmd[51] = 0x00;
                cmd[52] = 0x00;
                /* 最大充电、放电电流 */
                cmd[53] = sh367601xb.bms_system.current_mgr.data.max_charge_current >> 24;
                cmd[54] = sh367601xb.bms_system.current_mgr.data.max_charge_current >> 16;
                cmd[55] = sh367601xb.bms_system.current_mgr.data.max_charge_current >> 8;
                cmd[56] = sh367601xb.bms_system.current_mgr.data.max_charge_current;
                cmd[57] = sh367601xb.bms_system.current_mgr.data.max_discharge_current >> 24;
                cmd[58] = sh367601xb.bms_system.current_mgr.data.max_discharge_current >> 16;
                cmd[59] = sh367601xb.bms_system.current_mgr.data.max_discharge_current >> 8;
                cmd[60] = sh367601xb.bms_system.current_mgr.data.max_discharge_current;

                /* 校验 */
                u16 check;
                check = crc16(&cmd[1], 60);
                cmd[61] = check >> 8;
                cmd[62] = check;
                cmd[63] = 0x0D;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            /* 单体电压 */
            case 0x02:
            {
                unsigned char cmd[80] = {0};
                unsigned short check;
                u8 batt_num = sh367601xb.bms_system.voltage_mgr.data.battery_count;
                
                cmd[0] = 0x7E;
                cmd[1] = 0x01;
                cmd[2] = 0x02;
                cmd[3] = batt_num * 2 + 2;
                cmd[4] = 0x00;
                cmd[5] = batt_num;
                
                /* 填充单体电压数据 */
                for (unsigned short i = 0; i < batt_num; i++)
                {
                    cmd[6 + (i * 2)] = sh367601xb.bms_system.voltage_mgr.data.cell_voltages[i] >> 8;
                    cmd[7 + (i * 2)] = sh367601xb.bms_system.voltage_mgr.data.cell_voltages[i];
                }
                
                check = crc16(&cmd[1], 6 + (batt_num * 2));
                cmd[7 + (batt_num * 2)] = check >> 8;
                cmd[8 + (batt_num * 2)] = check;
                cmd[9 + (batt_num * 2)] = 0x0D;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, 10 + (batt_num * 2));
                break;
            }
            /* 单点温度 */
            case 0x03:
            {
                unsigned char cmd[17] = {0};
                u16 check;
                cmd[0] = 0x7E;
                cmd[1] = 0x01;
                cmd[2] = 0x03;
                cmd[3] = 0x07;
                cmd[4] = 0x00;
                cmd[5] = 0x00;
                cmd[6] = 0x00;
                /* 外部温度 */
                cmd[7] = 0x06;
                cmd[8] =  sh367601xb.bms_system.temp_mgr.data.external_temp[0];
                cmd[9] =  sh367601xb.bms_system.temp_mgr.data.external_temp[1];
                cmd[10] = sh367601xb.bms_system.temp_mgr.data.external_temp[2];
                cmd[11] = sh367601xb.bms_system.temp_mgr.data.external_temp[3];
                cmd[12] = sh367601xb.bms_system.temp_mgr.data.external_temp[4];
                cmd[13] = sh367601xb.bms_system.temp_mgr.data.external_temp[5];
                check = checksum(&cmd[1], 13);
                cmd[14] = check >> 8;
                cmd[15] = check;
                cmd[16] = 0x0D;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, sizeof(cmd));
                break;
            }
            default:
                break;
        }
    }
    /* 读取系统信息 */
    else if (data[2] <= 0x30)
    {
        switch (data[2])
        {
            /* 读取产品信息 */
            case 0x20:
            {
                /* 读取产品信息 */
                u8 cmd[30] = {0};
                cmd[0] = 0x7E;
                cmd[1] = 0x01;
                cmd[2] = 0x20;
                cmd[3] = 0x18;  /* 数据长度 */
                
                /* 填充产品信息 */
                memcpy(&cmd[4], "SH367601B_BMS", 13);  /* 产品名称 */
                cmd[17] = 0x01;  /* 硬件版本 */
                cmd[18] = 0x00;  /* 软件版本 */
                cmd[19] = sh367601xb.chip.data.chip_count;  /* 芯片数量 */
                cmd[20] = (u8)sh367601xb.chip.data.chip_type[0];  /* 芯片类型 */
                
                u16 check = crc16(&cmd[1], 23);
                cmd[24] = check >> 8;
                cmd[25] = check;
                cmd[26] = 0x0D;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, 27);
                break;
            }
            /* 读取产品序列号 */
            case 0x21:
            {
                /* 读取序列号 */
                u8 cmd[30] = {0};
                cmd[0] = 0x7E;
                cmd[1] = 0x01;
                cmd[2] = 0x21;
                cmd[3] = 0x10;  /* 数据长度 */
                
                /* 填充序列号（从Flash或配置中读取） */
                memcpy(&cmd[4], "BMS202400001", 12);  /* 示例序列号 */
                
                u16 check = crc16(&cmd[1], 19);
                cmd[20] = check >> 8;
                cmd[21] = check;
                cmd[22] = 0x0D;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, 23);
                break;
            }
            /* 读取生产信息 */
            case 0x24:
            {
                /* 读取生产信息 */
                u8 cmd[20] = {0};
                cmd[0] = 0x7E;
                cmd[1] = 0x01;
                cmd[2] = 0x24;
                cmd[3] = 0x08;  /* 数据长度 */
                
                /* 填充生产日期和订单号 */
                cmd[4] = 0x20;  /* 年份高位 */
                cmd[5] = 0x24;  /* 年份低位 */
                cmd[6] = 0x01;  /* 月份 */
                cmd[7] = 0x01;  /* 日期 */
                cmd[8] = 0x00;  /* 订单号高位 */
                cmd[9] = 0x01;  /* 订单号低位 */
                cmd[10] = 0x00; /* 预留 */
                cmd[11] = 0x00; /* 预留 */
                
                u16 check = crc16(&cmd[1], 11);
                cmd[12] = check >> 8;
                cmd[13] = check;
                cmd[14] = 0x0D;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, 15);
                break;
            }
            default:
                break;
        }
    }
    /* 设置参数 */
    else if (data[2] <= 0x4F)
    {
        switch (data[2])
        {
            /* 设置产品序列号 */
            case 0x41:
            {
                /* 设置序列号的响应 */
                u8 cmd[10] = {0};
                cmd[0] = 0x7E;
                cmd[1] = 0x01;
                cmd[2] = 0x41;
                cmd[3] = 0x01;  /* 数据长度 */
                cmd[4] = 0x00;  /* 设置结果：0=成功 */
                
                u16 check = crc16(&cmd[1], 4);
                cmd[5] = check >> 8;
                cmd[6] = check;
                cmd[7] = 0x0D;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, 8);
                break;
            }
            /* 设置出厂信息 */
            case 0x44:
            {
                /* 设置出厂信息的响应 */
                u8 cmd[10] = {0};
                cmd[0] = 0x7E;
                cmd[1] = 0x01;
                cmd[2] = 0x44;
                cmd[3] = 0x01;  /* 数据长度 */
                cmd[4] = 0x00;  /* 设置结果：0=成功 */
                
                u16 check = crc16(&cmd[1], 4);
                cmd[5] = check >> 8;
                cmd[6] = check;
                cmd[7] = 0x0D;
                blc_gatt_pushHandleValueNotify(BLM_CONN_HANDLE, SPP_SERVER_TO_CLIENT_DP_H, cmd, 8);
                break;
            }
            default:
                break;
        }
    }
}
#endif