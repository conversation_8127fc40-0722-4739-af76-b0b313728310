{"artifacts": [{"path": "825x_ble_sample"}, {"path": "825x_ble_sample.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_options", "target_link_directories", "target_link_libraries", "target_compile_options"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 37, "parent": 0}, {"command": 1, "file": 0, "line": 64, "parent": 0}, {"command": 2, "file": 0, "line": 63, "parent": 0}, {"command": 3, "file": 0, "line": 66, "parent": 0}, {"command": 4, "file": 0, "line": 43, "parent": 0}, {"command": 4, "file": 0, "line": 49, "parent": 0}, {"command": 4, "file": 0, "line": 53, "parent": 0}, {"command": 4, "file": 0, "line": 57, "parent": 0}, {"command": 4, "file": 0, "line": 61, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g"}, {"backtrace": 5, "fragment": "-DMCU_STARTUP_8258"}, {"backtrace": 6, "fragment": "-ffunction-sections"}, {"backtrace": 6, "fragment": "-fdata-sections"}, {"backtrace": 6, "fragment": "-Wall"}], "language": "ASM", "sourceIndexes": [0, 13]}, {"compileCommandFragments": [{"backtrace": 7, "fragment": "-D__PROJECT_8258_BLE_SAMPLE__=1"}, {"backtrace": 7, "fragment": "-DCHIP_TYPE=CHIP_TYPE_825x"}, {"backtrace": 8, "fragment": "-ID:/Telink_Project/BMS_Base/././"}, {"backtrace": 8, "fragment": "-ID:/Telink_Project/BMS_Base/./vendor/common"}, {"backtrace": 8, "fragment": "-ID:/Telink_Project/BMS_Base/./common"}, {"backtrace": 8, "fragment": "-ID:/Telink_Project/BMS_Base/./drivers/8258"}, {"backtrace": 8, "fragment": "-ID:/Telink_Project/BMS_Base/./vendor/user_app/bms"}, {"backtrace": 8, "fragment": "-ID:/Telink_Project/BMS_Base/./vendor/user_app/bms/zhongying"}, {"backtrace": 9, "fragment": "-fpack-struct"}, {"backtrace": 9, "fragment": "-fshort-enums"}, {"backtrace": 9, "fragment": "-O2"}, {"backtrace": 9, "fragment": "-std=gnu99"}, {"backtrace": 9, "fragment": "-fshort-wchar"}, {"backtrace": 9, "fragment": "-fms-extensions"}, {"backtrace": 9, "fragment": "-finline-small-functions"}, {"backtrace": 9, "fragment": "-ffunction-sections"}, {"backtrace": 9, "fragment": "-fdata-sections"}, {"backtrace": 9, "fragment": "-Wall"}], "language": "C", "sourceIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83]}], "id": "825x_ble_sample::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"backtrace": 2, "fragment": "-TD:/Telink_Project/BMS_Base/././boot.link", "role": "flags"}, {"backtrace": 2, "fragment": "--gc-sections", "role": "flags"}, {"backtrace": 3, "fragment": "-LD:\\Telink_Project\\BMS_Base\\.\\proj_lib", "role": "libraryPath"}, {"backtrace": 4, "fragment": "-llt_825x", "role": "libraries"}, {"backtrace": 4, "fragment": "-lsoft-fp", "role": "libraries"}, {"backtrace": 4, "fragment": "-llt_general_stack", "role": "libraries"}], "language": "C"}, "name": "825x_ble_sample", "nameOnDisk": "825x_ble_sample", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0, 13]}, {"name": "Source Files", "sourceIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "div_mod.S", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "application/app/usbaud.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "application/app/usbcdc.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "application/app/usbkb.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "application/app/usbmouse.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "application/audio/adpcm.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "application/audio/gl_audio.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "application/audio/tl_audio.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "application/keyboard/keyboard.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "application/print/putchar.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "application/print/u_printf.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "application/usbstd/usb.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "application/usbstd/usbdesc.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "boot/B85/cstartup_825x.S", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "common/sdk_version.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "common/string.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "common/utility.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/adc.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/aes.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/analog.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/audio.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/bsp.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/clock.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/driver_ext/ext_calibration.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/driver_ext/ext_misc.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/driver_ext/rf_pa.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/driver_ext/software_uart.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/emi.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/flash.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/flash/flash_mid011460c8.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/flash/flash_mid1060c8.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/flash/flash_mid13325e.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/flash/flash_mid134051.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/flash/flash_mid136085.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/flash/flash_mid1360c8.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/flash/flash_mid1360eb.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/flash/flash_mid14325e.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/flash/flash_mid1460c8.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/gpio.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/i2c.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/lpc.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/qdec.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/s7816.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/spi.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/timer.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/uart.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/usbhw.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "drivers/8258/watchdog.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/b85m_ble_sample/app.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/b85m_ble_sample/app_att.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/b85m_ble_sample/app_ui.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/b85m_ble_sample/main.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/common/app_buffer.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/common/app_common.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/common/battery_check.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/common/ble_flash.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/common/blt_fw_sign.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/common/blt_led.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/common/blt_soft_timer.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/common/custom_pair.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/common/flash_fw_check.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/common/flash_prot.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/common/simple_sdp.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/common/tlkapi_debug.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/common/user_config.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/agreement/agreement.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/agreement/fn/fn_agreement.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/agreement/rzn/rzn_agreement.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/at_cmd/app_at_cmd.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/att_ble/app_ble.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/bms/bms_data.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/bms/bms_data_tool.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/bms/zy/sh367601xb.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/bms/zy/sh367601xb_chip.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/bms/zy/sh367601xb_communication.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/bms/zy/sh367601xb_config.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/bms/zy/sh367601xb_converter.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/bms/zy/sh367601xb_parser.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/bms/zy/sh367601xb_tool.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/flash/user_app_flash.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/list/queue/queue.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/system_main/app_mian.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/uart/app_usart.c", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 1, "path": "vendor/user_app/user_app_main.c", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}