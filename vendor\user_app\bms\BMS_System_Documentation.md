# BMS系统架构文档

## 概述

本文档详细描述了基于*********芯片的BMS（电池管理系统）完整架构设计、模块组成和使用方法。该BMS系统采用面向对象的设计模式，集成了完整的电池管理功能和多芯片硬件切换机制。

## 目录结构

```
vendor/user_app/bms/
├── bms_data.h              # BMS数据管理系统核心头文件
├── bms_data.c              # BMS数据管理系统实现
├── bms_data_tool.h         # BMS工具函数头文件
├── bms_data_tool.c         # BMS工具函数实现
└── zy/                     # 中颖(ZY)芯片驱动模块
    ├── sh367601xb.h        # *********芯片主头文件
    ├── sh367601xb.c        # *********芯片主实现
    ├── sh367601xb_chip.h   # 芯片管理模块头文件
    ├── sh367601xb_chip.c   # 芯片管理模块实现
    ├── sh367601xb_communication.h  # 通信驱动头文件
    ├── sh367601xb_communication.c  # 通信驱动实现
    ├── sh367601xb_config.h         # 配置管理头文件
    ├── sh367601xb_config.c         # 配置管理实现
    ├── sh367601xb_converter.h      # 数据转换头文件
    ├── sh367601xb_converter.c      # 数据转换实现
    ├── sh367601xb_parser.h         # 数据解析头文件
    ├── sh367601xb_parser.c         # 数据解析实现
    ├── sh367601xb_tool.h           # 工具函数头文件
    └── sh367601xb_tool.c           # 工具函数实现
```

## 系统架构

### 1. 核心架构设计

BMS系统采用分层架构设计：

```
┌─────────────────────────────────────────┐
│           应用层 (Application)           │
├─────────────────────────────────────────┤
│         BMS数据管理层 (BMS Core)         │
│  ┌─────────────────────────────────────┐ │
│  │        BMS_DataManager              │ │
│  │  ┌─────────────────────────────────┐│ │
│  │  │ AlarmManager                    ││ │
│  │  │ StatusManager                   ││ │
│  │  │ VoltageManager                  ││ │
│  │  │ CurrentManager                  ││ │
│  │  │ ChargeDischargeManager          ││ │
│  │  │ TemperatureManager              ││ │
│  │  │ BatteryStateManager             ││ │
│  │  │ ProtectionParameterManager      ││ │
│  │  │ CustomParameterManager          ││ │
│  │  └─────────────────────────────────┘│ │
│  └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│        芯片驱动层 (Chip Driver)          │
│  ┌─────────────────────────────────────┐ │
│  │        *********_Device             │ │
│  │  ┌─────────────────────────────────┐│ │
│  │  │ Config Module                   ││ │
│  │  │ Communication Module            ││ │
│  │  │ Parser Module                   ││ │
│  │  │ Converter Module                ││ │
│  │  │ Tool Module                     ││ │
│  │  │ Chip Module (多芯片管理)        ││ │
│  │  └─────────────────────────────────┘│ │
│  └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│         硬件抽象层 (HAL)                 │
│  ┌─────────────────────────────────────┐ │
│  │ 芯片硬件切换函数指针数组              │ │
│  │ chip_switch_functions[MAX_CHIP_COUNT]│ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

### 2. 主要组件

#### 2.1 BMS数据管理系统 (bms_data.h/c)

**核心管理器模块：**

- **AlarmManager** - 告警管理器
  - 过压/欠压告警
  - 过流/欠流告警
  - 温度异常告警
  - 短路告警

- **StatusManager** - 状态管理器
  - 充电/放电状态
  - MOS管状态
  - 均衡状态

- **VoltageManager** - 电压管理器
  - 单体电压监测
  - 总电压计算
  - 最高/最低电压统计

- **CurrentManager** - 电流管理器
  - 充电/放电电流监测
  - 电流状态判断
  - 电流滤波处理

- **ChargeDischargeManager** - 充放电管理器
  - SOC计算
  - 剩余容量计算
  - 功率计算
  - 循环次数统计

- **TemperatureManager** - 温度管理器
  - 外部温度监测
  - 芯片温度监测
  - MOS管温度监测

- **BatteryStateManager** - 电池状态管理器
  - SOH计算
  - 电池健康度评估

- **ProtectionParameterManager** - 保护参数管理器
  - 保护阈值设置
  - 保护延时配置

- **CustomParameterManager** - 自定义参数管理器
  - 电池容量配置
  - 用户自定义参数

#### 2.2 *********芯片驱动系统 (zy/)

**模块化设计：**

- **Config Module** (sh367601xb_config.h/c)
  - ROM配置参数设置
  - 保护阈值配置
  - 芯片功能配置

- **Communication Module** (sh367601xb_communication.h/c)
  - UART通信接口
  - 命令发送/接收
  - CRC校验

- **Parser Module** (sh367601xb_parser.h/c)
  - ROM/RAM数据解析
  - 寄存器数据格式化
  - 调试信息输出

- **Converter Module** (sh367601xb_converter.h/c)
  - ADC数据转换
  - 物理量换算
  - 单位转换

- **Tool Module** (sh367601xb_tool.h/c)
  - NTC温度计算
  - 电流计算
  - 滤波算法
  - 电压转换

- **Chip Module** (sh367601xb_chip.h/c) - **新增多芯片硬件切换功能**
  - 多芯片管理
  - 硬件切换控制
  - 批量操作
  - 任务流程管理

### 3. 数据结构

#### 3.1 核心数据类型

```c
// 电流状态枚举
typedef enum {
    CURRENT_STATE_IDLE = 0,     /* 空闲状态 */
    CURRENT_STATE_CHARGING,     /* 充电状态 */
    CURRENT_STATE_DISCHARGING   /* 放电状态 */
} CurrentState;

// 芯片类型定义
typedef enum {
    *********_CHIP_10_SERIES = 0,    /* 10串芯片 */
    *********_CHIP_14_SERIES = 1,    /* 14串芯片 */
    *********_CHIP_16_SERIES = 2     /* 16串芯片 */
} *********_ChipType;
```

#### 3.2 主要数据结构

```c
// BMS主数据管理器
typedef struct BMS_DataManager {
    AlarmManager alarm_mgr;
    StatusManager status_mgr;
    VoltageManager voltage_mgr;
    CurrentManager current_mgr;
    ChargeDischargeManager charge_discharge_mgr;
    TemperatureManager temp_mgr;
    BatteryStateManager battery_state_mgr;
    ProtectionParameterManager protection_param_mgr;
    CustomParameterManager custom_param_mgr;
    bool is_initialized;
} BMS_DataManager;

// 芯片管理模块 - 新增硬件切换功能
typedef struct {
    struct {
        *********_ChipType chip_type[MAX_CHIP_COUNT];
        unsigned char chip_count;
        unsigned char current_chip_index;
        unsigned char write_rom_in_progress;
        unsigned char write_rom_target_chip_count;
        /* 芯片硬件切换函数指针数组 - 新功能 */
        void (*chip_switch_functions[MAX_CHIP_COUNT])(void);
    } data;
    
    struct {
        void (*switch_to_chip)(*********_Device* device, unsigned char chip_index);
        void (*write_rom_for_chip)(*********_Device* device, Queue* q);
        void (*set_chip_count)(*********_Device* device, unsigned char chip_count);
        void (*start_batch_write_rom)(*********_Device* device, Queue* q);
        void (*set_chip_type)(*********_Device* device, unsigned char chip_index, *********_ChipType chip_type);
        *********_ChipType (*get_chip_type)(*********_Device* device, unsigned char chip_index);
        unsigned char (*get_chip_max_cells)(*********_Device* device, unsigned char chip_index);
        /* 新增任务流程管理方法 */
        int (*handle_reset_task_chip_switching)(*********_Device* device, Queue* q);
        int (*handle_ram_read_chip_switching)(*********_Device* device, Queue* q, bool is_rom_task);
        void (*set_chip_switch_function)(*********_Device* device, unsigned char chip_index, void (*switch_func)(void));
    } method;
} *********_Chip;

// *********设备主类
struct *********_Device {
    BMS_DataManager bms_system;                    /* 内嵌BMS系统 */
    User_App_Sh3607601x_Rom_TypeDef rom[MAX_CHIP_COUNT];
    User_App_Sh3607601x_Ram_TypeDef ram[MAX_CHIP_COUNT];
    User_App_Sh3607601x_Rom_TypeDef write_rom;     /* 写ROM副本 */
    unsigned char write_buff[ROM_ADDR_LEN];        /* 写入数据缓冲区 */
    char write_flags[ROM_ADDR_LEN];                /* 写入标志数组 */
    
    /* 功能模块 */
    *********_Config config;
    *********_Communication comm;
    *********_Parser parser;
    *********_Converter converter;
    *********_Tool tool;
    *********_Chip chip;                           /* 增强的芯片管理模块 */
    
    /* 设备状态 */
    bool is_initialized;
    bool is_connected;
    
    /* BMS数据同步方法 */
    struct {
        void (*update_realtime_data)(*********_Device* self);
        void (*update_protection_config)(*********_Device* self);
    } bms_sync;
};
```

## 使用方法

### 1. 系统初始化

```c
#include "bms/zy/sh367601xb.h"

// 创建设备实例
*********_Device bms_device;

// 初始化BMS系统
int result = sh367601b_init(&bms_device);
if (result == 0) {
    printf("BMS system initialized successfully\n");
}
```

### 2. 多芯片硬件切换配置 - **新功能**

```c
// 定义芯片硬件切换函数
void chip0_switch_function(void) {
    // 芯片0的硬件切换逻辑（如GPIO控制）
    printf("Switching to chip 0\n");
    // 实际的硬件切换代码
}

void chip1_switch_function(void) {
    // 芯片1的硬件切换逻辑
    printf("Switching to chip 1\n");
    // 实际的硬件切换代码
}

// 注册芯片切换函数
bms_device.chip.method.set_chip_switch_function(&bms_device, 0, chip0_switch_function);
bms_device.chip.method.set_chip_switch_function(&bms_device, 1, chip1_switch_function);

// 设置芯片数量
bms_device.chip.method.set_chip_count(&bms_device, 2);
```

### 3. 数据读取和处理

```c
// 读取ROM配置
bms_device.comm.method.read_rom(ROM_ADDR_START, ROM_ADDR_LEN);
// 注意：实际应用中需要等待数据接收完成，然后调用解析函数

// 读取RAM传感器数据
bms_device.comm.method.read_ram(RAM_ADDR_START, RAM_ADDR_LEN);
// 注意：实际应用中需要等待数据接收完成，然后调用解析函数

// 更新BMS系统数据
bms_device.bms_sync.update_realtime_data(&bms_device);
```

### 4. 配置管理

```c
// 设置过压保护阈值
bms_device.config.method.set_ov(&bms_device, 4200);  // 4.2V

// 设置欠压保护阈值
bms_device.config.method.set_uv(&bms_device, 2500);  // 2.5V

// 设置充电过流保护
bms_device.config.method.set_occ(&bms_device, 10000); // 10A

// 设置电池串数
bms_device.config.method.set_cn(&bms_device, 0, 13);  // 13串电池
```

### 5. 多芯片管理 - **增强功能**

```c
// 设置芯片数量
bms_device.chip.method.set_chip_count(&bms_device, 2);

// 切换到指定芯片（自动调用硬件切换函数）
bms_device.chip.method.switch_to_chip(&bms_device, 1);

// 批量写ROM配置（自动遍历所有芯片）
bms_device.chip.method.start_batch_write_rom(&bms_device, &queue);
```

## 新增功能特性

### 1. 硬件切换函数指针数组

每个芯片可以注册独立的硬件切换函数：

```c
// 芯片硬件切换函数指针数组
void (*chip_switch_functions[MAX_CHIP_COUNT])(void);

// 设置单个芯片的硬件切换函数
void (*set_chip_switch_function)(*********_Device* device,
                                unsigned char chip_index,
                                void (*switch_func)(void));
```

### 2. 任务流程管理

新增任务流程管理方法，支持自动芯片切换：

```c
// 处理复位任务中的芯片切换逻辑
int (*handle_reset_task_chip_switching)(*********_Device* device, Queue* q);

// 处理RAM读取任务中的芯片切换逻辑
int (*handle_ram_read_chip_switching)(*********_Device* device, Queue* q, bool is_rom_task);
```

### 3. 智能BMS数据更新

只在最后一个芯片时更新BMS数据，避免重复计算：

```c
// 只有当前芯片是最后一个芯片时，才进行BMS数据更新
static void sh367601b_update_bms_from_ram(*********_Device* device)
{
    unsigned char last_chip_index = device->chip.data.chip_count - 1;
    if (device->chip.data.current_chip_index != last_chip_index) {
        return; // 跳过非最后芯片的BMS更新
    }
    // 执行BMS数据更新...
}
```

## API参考

### 1. 芯片管理API

#### 1.1 基本芯片操作
```c
// 切换到指定芯片
void sh36760_switch_to_chip(*********_Device* device, unsigned char chip_index);

// 设置芯片数量
void sh36760_set_chip_count(*********_Device* device, unsigned char chip_count);

// 设置芯片类型
void sh36760_set_chip_type(*********_Device* device, unsigned char chip_index, *********_ChipType chip_type);

// 获取芯片类型
*********_ChipType sh36760_get_chip_type(*********_Device* device, unsigned char chip_index);

// 获取芯片最大串数
unsigned char sh36760_get_chip_max_cells(*********_Device* device, unsigned char chip_index);
```

#### 1.2 硬件切换管理 - **新API**
```c
// 设置单个芯片的硬件切换函数
void sh36760_set_chip_switch_function(*********_Device* device,
                                     unsigned char chip_index,
                                     void (*switch_func)(void));
```

#### 1.3 任务流程管理 - **新API**
```c
// 处理复位任务中的芯片切换逻辑
int sh36760_handle_reset_task_chip_switching(*********_Device* device, Queue* q);

// 处理RAM读取任务中的芯片切换逻辑
int sh36760_handle_ram_read_chip_switching(*********_Device* device, Queue* q, bool is_rom_task);
```

#### 1.4 批量操作
```c
// 写入ROM数据到当前芯片
void sh36760_write_rom_for_chip(*********_Device* device, Queue* q);

// 启动批量写ROM流程
void sh36760_start_batch_write_rom(*********_Device* device, Queue* q);
```

## 完整使用示例

### 1. 多芯片BMS系统初始化示例

```c
#include "bms/zy/sh367601xb.h"
#include "user_app_main.h"

// 全局变量
*********_Device g_bms_device;
Queue g_command_queue;

// 芯片硬件切换函数实现
void chip0_hardware_switch(void) {
    // 芯片0的硬件切换逻辑
    printf("Hardware switch to chip 0\n");
    // 例如：设置GPIO引脚控制多路选择器
    // gpio_set_pin(CHIP_SELECT_PIN0, 1);
    // gpio_set_pin(CHIP_SELECT_PIN1, 0);
}

void chip1_hardware_switch(void) {
    // 芯片1的硬件切换逻辑
    printf("Hardware switch to chip 1\n");
    // gpio_set_pin(CHIP_SELECT_PIN0, 0);
    // gpio_set_pin(CHIP_SELECT_PIN1, 1);
}

void chip2_hardware_switch(void) {
    // 芯片2的硬件切换逻辑
    printf("Hardware switch to chip 2\n");
    // gpio_set_pin(CHIP_SELECT_PIN0, 1);
    // gpio_set_pin(CHIP_SELECT_PIN1, 1);
}

/**
 * @brief 多芯片BMS系统初始化
 */
void multi_chip_bms_init_example(void)
{
    printf("=== Multi-Chip BMS System Initialization ===\n");

    // 1. 初始化BMS设备
    int result = sh367601b_init(&g_bms_device);
    if (result != 0) {
        printf("Error: BMS device initialization failed\n");
        return;
    }

    // 2. 设置芯片数量（3个芯片）
    g_bms_device.chip.method.set_chip_count(&g_bms_device, 3);

    // 3. 注册每个芯片的硬件切换函数
    g_bms_device.chip.method.set_chip_switch_function(&g_bms_device, 0, chip0_hardware_switch);
    g_bms_device.chip.method.set_chip_switch_function(&g_bms_device, 1, chip1_hardware_switch);
    g_bms_device.chip.method.set_chip_switch_function(&g_bms_device, 2, chip2_hardware_switch);

    // 4. 设置每个芯片的类型
    g_bms_device.chip.method.set_chip_type(&g_bms_device, 0, *********_CHIP_16_SERIES);  // 16串
    g_bms_device.chip.method.set_chip_type(&g_bms_device, 1, *********_CHIP_14_SERIES);  // 14串
    g_bms_device.chip.method.set_chip_type(&g_bms_device, 2, *********_CHIP_10_SERIES);  // 10串

    // 5. 配置基本保护参数
    g_bms_device.config.method.set_ov(&g_bms_device, 4200);    // 过压4.2V
    g_bms_device.config.method.set_uv(&g_bms_device, 2500);    // 欠压2.5V
    g_bms_device.config.method.set_occ(&g_bms_device, 50);     // 充电过流5A
    g_bms_device.config.method.set_ocd1(&g_bms_device, 100);   // 放电过流10A

    // 6. 配置每个芯片的电池串数
    g_bms_device.config.method.set_cn(&g_bms_device, 0, 16);   // 芯片0: 16串
    g_bms_device.config.method.set_cn(&g_bms_device, 1, 14);   // 芯片1: 14串
    g_bms_device.config.method.set_cn(&g_bms_device, 2, 10);   // 芯片2: 10串

    printf("Multi-chip BMS system initialized successfully\n");
    printf("Total chips: 3, Total cells: 40 (16+14+10)\n");
}

/**
 * @brief 多芯片数据读取和处理示例
 */
void multi_chip_data_process_example(void)
{
    printf("=== Multi-Chip Data Processing ===\n");

    // 遍历所有芯片读取数据
    for (unsigned char chip_idx = 0; chip_idx < g_bms_device.chip.data.chip_count; chip_idx++) {
        printf("Processing chip %d...\n", chip_idx);

        // 1. 切换到指定芯片（自动调用硬件切换函数）
        g_bms_device.chip.method.switch_to_chip(&g_bms_device, chip_idx);

        // 2. 读取ROM配置数据
        g_bms_device.comm.method.read_rom(ROM_ADDR_START, ROM_ADDR_LEN);
        // 等待数据接收完成...

        // 3. 读取RAM传感器数据
        g_bms_device.comm.method.read_ram(RAM_ADDR_START, RAM_ADDR_LEN);
        // 等待数据接收完成...

        // 4. 显示芯片信息
        *********_ChipType chip_type = g_bms_device.chip.method.get_chip_type(&g_bms_device, chip_idx);
        unsigned char max_cells = g_bms_device.chip.method.get_chip_max_cells(&g_bms_device, chip_idx);
        printf("  Chip %d: Type=%d, Max cells=%d\n", chip_idx, chip_type, max_cells);
    }

    // 5. 更新BMS系统数据（只在最后一个芯片时执行）
    g_bms_device.bms_sync.update_realtime_data(&g_bms_device);

    // 6. 获取BMS数据
    VoltageManager* vmgr = &g_bms_device.bms_system.voltage_mgr;
    CurrentManager* cmgr = &g_bms_device.bms_system.current_mgr;
    TemperatureManager* tmgr = &g_bms_device.bms_system.temp_mgr;
    ChargeDischargeManager* cdmgr = &g_bms_device.bms_system.charge_discharge_mgr;

    // 7. 显示关键数据
    printf("BMS System Status:\n");
    printf("  Total Voltage: %d mV\n", vmgr->data.total_voltage);
    printf("  Max Cell Voltage: %d mV\n", vmgr->data.max_voltage);
    printf("  Min Cell Voltage: %d mV\n", vmgr->data.min_voltage);
    printf("  Current: %d mA\n", cmgr->data.total_current);
    printf("  SOC: %d.%02d%%\n", cdmgr->data.soc / 100, cdmgr->data.soc % 100);
    printf("  Max Temperature: %d°C\n", tmgr->data.max_external_temp);
    printf("  Min Temperature: %d°C\n", tmgr->data.min_external_temp);
}

/**
 * @brief 批量写ROM配置示例
 */
void batch_rom_write_example(void)
{
    printf("=== Batch ROM Write Example ===\n");

    // 1. 启动批量写ROM流程（自动遍历所有芯片）
    printf("Starting batch ROM write for all chips...\n");
    g_bms_device.chip.method.start_batch_write_rom(&g_bms_device, &g_command_queue);

    // 2. 在队列处理中，会自动调用芯片切换逻辑
    // 当处理QUEUE_RESET_TASK时，会调用handle_reset_task_chip_switching
    // 自动切换到下一个芯片并继续写ROM流程

    printf("Batch ROM write initiated. Processing will continue in queue handler.\n");
}

/**
 * @brief 任务流程管理示例
 */
void task_flow_management_example(void)
{
    printf("=== Task Flow Management Example ===\n");

    // 模拟在队列处理中的芯片切换逻辑

    // 1. 在复位任务中处理芯片切换
    printf("Handling reset task chip switching...\n");
    int result = g_bms_device.chip.method.handle_reset_task_chip_switching(&g_bms_device, &g_command_queue);
    if (result == 0) {
        printf("Switched to next chip, continuing ROM write\n");
    } else {
        printf("All chips ROM write completed\n");
    }

    // 2. 在RAM读取任务中处理芯片切换
    printf("Handling RAM read task chip switching...\n");
    result = g_bms_device.chip.method.handle_ram_read_chip_switching(&g_bms_device, &g_command_queue, false);
    if (result == 0) {
        printf("Switched to next chip, continuing RAM read\n");
    } else {
        printf("All chips RAM read completed\n");
    }
}

/**
 * @brief 主程序示例
 */
void bms_main_example(void)
{
    printf("========================================\n");
    printf("Multi-Chip BMS System Complete Example\n");
    printf("========================================\n");

    // 1. 系统初始化
    multi_chip_bms_init_example();

    // 2. 数据处理
    multi_chip_data_process_example();

    // 3. 批量写ROM
    batch_rom_write_example();

    // 4. 任务流程管理
    task_flow_management_example();

    printf("========================================\n");
    printf("BMS Example Completed\n");
    printf("========================================\n");
}
```

## 特性和优势

### 1. 面向对象设计
- 模块化架构，易于维护和扩展
- 清晰的接口定义和数据封装
- 函数指针实现多态性

### 2. 完整的BMS功能
- 电压、电流、温度监测
- SOC/SOH计算
- 多级保护机制
- 均衡控制

### 3. 多芯片支持 - **增强功能**
- 支持最多8个芯片级联
- 硬件切换函数指针数组
- 自动芯片切换和批量操作
- 智能任务流程管理

### 4. 高精度计算
- NTC温度精确计算
- 电流滤波算法
- SOC库仑计法

### 5. 调试友好
- 详细的调试输出
- 数据格式化显示
- 状态监控接口

### 6. 智能数据管理 - **新特性**
- 只在最后一个芯片时更新BMS数据
- 避免重复计算，提高效率
- 自动收集所有芯片数据

## 配置参数

### 1. 保护参数配置
- 过压保护：3.6V - 4.5V
- 欠压保护：2.0V - 3.2V
- 充电过流：1A - 20A
- 放电过流：1A - 50A
- 温度保护：-40℃ - +85℃

### 2. 系统参数
- 最大芯片数：8个
- 最大电池串数：16串/芯片
- 通信波特率：9600 - 115200
- 采样精度：12位ADC

### 3. 芯片类型支持
- *********_CHIP_10_SERIES：最大10串
- *********_CHIP_14_SERIES：最大14串
- *********_CHIP_16_SERIES：最大16串

## 重要常量

### 地址定义
```c
#define ROM_ADDR_START 0x00     // ROM起始地址
#define ROM_ADDR_LEN   0x15     // ROM长度(21字节)
#define RAM_ADDR_START 0x40     // RAM起始地址
#define RAM_ADDR_LEN   0x2E     // RAM长度(46字节)
```

### 芯片参数
```c
#define MAX_CHIP_COUNT 8        // 最大芯片数量
#define MAX_CELLS_10_SERIES 10  // 10串芯片最大串数
#define MAX_CELLS_14_SERIES 14  // 14串芯片最大串数
#define MAX_CELLS_16_SERIES 16  // 16串芯片最大串数
```

## 注意事项

### 1. 硬件切换函数
- 必须为每个芯片注册硬件切换函数
- 切换函数应包含实际的硬件控制逻辑
- 确保切换时序正确

### 2. 数据同步
- BMS数据只在最后一个芯片时更新
- 读取数据后需调用`update_realtime_data()`
- 注意芯片索引的有效性检查

### 3. 任务流程
- 批量操作会自动管理芯片切换
- 在队列处理中正确调用流程管理函数
- 确保队列任务的正确顺序

### 4. 内存管理
- 注意数组边界检查
- 芯片索引不能超过MAX_CHIP_COUNT
- 静态分配避免内存碎片

## 版本信息

- **版本**: 2.0 (新增多芯片硬件切换功能)
- **日期**: 2025
- **开发者**: 嵌入式BMS团队
- **芯片支持**: *********系列
- **编译器**: TC32 GCC
- **平台**: Telink 825x

## 更新日志

### v2.0 (2025)
- ✅ 新增硬件切换函数指针数组
- ✅ 新增任务流程管理API
- ✅ 优化BMS数据更新逻辑
- ✅ 增强多芯片管理功能
- ✅ 改进电流计算API

### v1.0 (2024)
- ✅ 基础BMS数据管理系统
- ✅ *********芯片驱动
- ✅ 基本多芯片支持
- ✅ 配置管理功能

---

本文档提供了BMS系统的完整技术说明，包括最新的多芯片硬件切换功能、架构设计、模块功能、使用方法、API参考和完整示例代码。如需更详细的实现细节，请参考各模块的源代码文件。

**使用的模型版本**: Claude Sonnet 4
