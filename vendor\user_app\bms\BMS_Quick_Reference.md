# BMS系统快速参考 v2.0

## 文件结构概览

```
vendor/user_app/bms/
├── 📁 核心BMS系统
│   ├── bms_data.h/c           # BMS数据管理核心
│   └── bms_data_tool.h/c      # BMS工具函数
└── 📁 zy/ (中颖芯片驱动)
    ├── sh367601xb.h/c         # 主设备驱动
    ├── sh367601xb_chip.h/c    # 芯片管理 (新增硬件切换)
    ├── sh367601xb_communication.h/c  # 通信驱动
    ├── sh367601xb_config.h/c  # 配置管理
    ├── sh367601xb_converter.h/c      # 数据转换
    ├── sh367601xb_parser.h/c  # 数据解析
    └── sh367601xb_tool.h/c    # 工具函数
```

## 核心数据结构

### BMS_DataManager (主管理器)
```c
typedef struct BMS_DataManager {
    AlarmManager alarm_mgr;                    // 告警管理
    StatusManager status_mgr;                  // 状态管理
    VoltageManager voltage_mgr;                // 电压管理
    CurrentManager current_mgr;                // 电流管理
    ChargeDischargeManager charge_discharge_mgr; // 充放电管理
    TemperatureManager temp_mgr;               // 温度管理
    BatteryStateManager battery_state_mgr;    // 电池状态管理
    ProtectionParameterManager protection_param_mgr; // 保护参数管理
    CustomParameterManager custom_param_mgr;  // 自定义参数管理
    bool is_initialized;
} BMS_DataManager;
```

### SH367601B_Chip (芯片管理模块) - **v2.0新增功能**
```c
typedef struct {
    struct {
        SH367601B_ChipType chip_type[MAX_CHIP_COUNT];
        unsigned char chip_count;
        unsigned char current_chip_index;
        unsigned char write_rom_in_progress;
        unsigned char write_rom_target_chip_count;
        /* 🆕 芯片硬件切换函数指针数组 */
        void (*chip_switch_functions[MAX_CHIP_COUNT])(void);
    } data;
    
    struct {
        void (*switch_to_chip)(SH367601B_Device* device, unsigned char chip_index);
        void (*write_rom_for_chip)(SH367601B_Device* device, Queue* q);
        void (*set_chip_count)(SH367601B_Device* device, unsigned char chip_count);
        void (*start_batch_write_rom)(SH367601B_Device* device, Queue* q);
        void (*set_chip_type)(SH367601B_Device* device, unsigned char chip_index, SH367601B_ChipType chip_type);
        SH367601B_ChipType (*get_chip_type)(SH367601B_Device* device, unsigned char chip_index);
        unsigned char (*get_chip_max_cells)(SH367601B_Device* device, unsigned char chip_index);
        /* 🆕 任务流程管理方法 */
        int (*handle_reset_task_chip_switching)(SH367601B_Device* device, Queue* q);
        int (*handle_ram_read_chip_switching)(SH367601B_Device* device, Queue* q, bool is_rom_task);
        void (*set_chip_switch_function)(SH367601B_Device* device, unsigned char chip_index, void (*switch_func)(void));
    } method;
} SH367601B_Chip;
```

## 快速使用指南

### 1. 基本初始化
```c
#include "bms/zy/sh367601xb.h"

SH367601B_Device bms_device;

// 初始化BMS系统
int result = sh367601b_init(&bms_device);
```

### 2. 多芯片硬件切换配置 - **🆕 v2.0新功能**
```c
// 定义芯片硬件切换函数
void chip0_switch(void) {
    printf("Switch to chip 0\n");
    // 实际硬件切换逻辑（如GPIO控制）
}

void chip1_switch(void) {
    printf("Switch to chip 1\n");
    // 实际硬件切换逻辑
}

// 注册芯片切换函数
bms_device.chip.method.set_chip_switch_function(&bms_device, 0, chip0_switch);
bms_device.chip.method.set_chip_switch_function(&bms_device, 1, chip1_switch);

// 设置芯片数量
bms_device.chip.method.set_chip_count(&bms_device, 2);
```

### 3. 配置保护参数
```c
// 电压保护
bms_device.config.method.set_ov(&bms_device, 4200);    // 过压4.2V
bms_device.config.method.set_uv(&bms_device, 2500);    // 欠压2.5V

// 电流保护
bms_device.config.method.set_occ(&bms_device, 50);     // 充电过流5A
bms_device.config.method.set_ocd1(&bms_device, 100);   // 放电过流10A

// 温度保护
bms_device.config.method.set_otc(&bms_device, 45);     // 充电高温45℃
bms_device.config.method.set_utc(&bms_device, 0);      // 充电低温0℃

// 电池串数
bms_device.config.method.set_cn(&bms_device, 0, 13);   // 13串电池
```

### 4. 数据读取
```c
// 读取ROM配置
bms_device.comm.method.read_rom(ROM_ADDR_START, ROM_ADDR_LEN);

// 读取RAM传感器数据
bms_device.comm.method.read_ram(RAM_ADDR_START, RAM_ADDR_LEN);

// 更新BMS数据 (只在最后一个芯片时执行)
bms_device.bms_sync.update_realtime_data(&bms_device);
```

### 5. 获取BMS数据
```c
VoltageManager* vmgr = &bms_device.bms_system.voltage_mgr;
CurrentManager* cmgr = &bms_device.bms_system.current_mgr;
TemperatureManager* tmgr = &bms_device.bms_system.temp_mgr;
ChargeDischargeManager* cdmgr = &bms_device.bms_system.charge_discharge_mgr;

// 关键数据
unsigned short total_voltage = vmgr->data.total_voltage;    // 总电压(mV)
int current = cmgr->data.total_current;                     // 电流(mA)
unsigned short soc = cdmgr->data.soc;                       // SOC(0.01%)
signed char max_temp = tmgr->data.max_external_temp;        // 最高温度(℃)
```

### 6. 多芯片管理 - **🆕 增强功能**
```c
// 切换芯片 (自动调用硬件切换函数)
bms_device.chip.method.switch_to_chip(&bms_device, 1);

// 批量写ROM (自动遍历所有芯片)
bms_device.chip.method.start_batch_write_rom(&bms_device, &queue);

// 任务流程管理
int result = bms_device.chip.method.handle_reset_task_chip_switching(&bms_device, &queue);
```

## 常用API速查

### 配置管理 (Config)
| 函数 | 功能 | 参数 |
|------|------|------|
| `set_ov()` | 设置过压保护 | 电压值(mV) |
| `set_uv()` | 设置欠压保护 | 电压值(mV) |
| `set_occ()` | 设置充电过流 | 电流值(0.1A) |
| `set_ocd1()` | 设置放电过流 | 电流值(0.1A) |
| `set_otc()` | 设置充电高温 | 温度值(℃) |
| `set_utc()` | 设置充电低温 | 温度值(℃) |
| `set_cn()` | 设置电池串数 | 芯片索引, 串数 |

### 通信驱动 (Communication)
| 函数 | 功能 | 参数 |
|------|------|------|
| `reset()` | 复位芯片 | 无 |
| `write_command()` | 发送写命令 | 无 |
| `read_rom()` | 读ROM数据 | 地址, 长度 |
| `read_ram()` | 读RAM数据 | 地址, 长度 |

### 芯片管理 (Chip) - **🆕 v2.0增强**
| 函数 | 功能 | 参数 |
|------|------|------|
| `switch_to_chip()` | 切换芯片 | 设备指针, 芯片索引 |
| `set_chip_count()` | 设置芯片数量 | 设备指针, 芯片数量 |
| `write_rom_for_chip()` | 写ROM到芯片 | 设备指针, 队列指针 |
| `start_batch_write_rom()` | 批量写ROM | 设备指针, 队列指针 |
| `set_chip_switch_function()` | **🆕 设置硬件切换函数** | 设备指针, 芯片索引, 函数指针 |
| `handle_reset_task_chip_switching()` | **🆕 处理复位任务切换** | 设备指针, 队列指针 |
| `handle_ram_read_chip_switching()` | **🆕 处理RAM读取切换** | 设备指针, 队列指针, 是否ROM任务 |

### 数据解析 (Parser)
| 函数 | 功能 | 参数 |
|------|------|------|
| `parse_rom()` | 解析ROM数据 | ROM结构体, 数据缓冲区 |
| `parse_ram()` | 解析RAM数据 | RAM结构体, 数据缓冲区 |
| `print_rom()` | 打印ROM信息 | 设备指针 |
| `print_ram()` | 打印RAM信息 | 设备指针 |

## 重要常量

### 地址定义
```c
#define ROM_ADDR_START 0x00     // ROM起始地址
#define ROM_ADDR_LEN   0x15     // ROM长度(21字节)
#define RAM_ADDR_START 0x40     // RAM起始地址
#define RAM_ADDR_LEN   0x2E     // RAM长度(46字节)
```

### 芯片类型
```c
SH367601B_CHIP_10_SERIES = 0    // 10串芯片
SH367601B_CHIP_14_SERIES = 1    // 14串芯片
SH367601B_CHIP_16_SERIES = 2    // 16串芯片
```

### 电流状态
```c
CURRENT_STATE_IDLE = 0          // 空闲状态
CURRENT_STATE_CHARGING = 1      // 充电状态
CURRENT_STATE_DISCHARGING = 2   // 放电状态
```

## v2.0新特性

### 🆕 硬件切换函数指针
- 每个芯片可注册独立的硬件切换函数
- 自动调用硬件切换逻辑
- 支持GPIO控制、多路选择器等

### 🆕 任务流程管理
- 自动芯片切换和任务继续
- 批量操作智能管理
- 减少手动干预

### 🆕 智能BMS数据更新
- 只在最后一个芯片时更新BMS数据
- 避免重复计算，提高效率
- 自动收集所有芯片数据

### 🆕 改进的电流计算
- 简化电流计算API参数
- 优化计算精度
- 更好的滤波算法

## 调试技巧

### 1. 数据监控
```c
// 打印ROM配置
bms_device.parser.method.print_rom(&bms_device);

// 打印RAM传感器数据
bms_device.parser.method.print_ram(&bms_device);
```

### 2. 芯片切换调试
```c
// 检查当前芯片索引
printf("Current chip: %d\n", bms_device.chip.data.current_chip_index);

// 检查芯片数量
printf("Total chips: %d\n", bms_device.chip.data.chip_count);

// 检查写ROM流程状态
printf("Write ROM in progress: %d\n", bms_device.chip.data.write_rom_in_progress);
```

### 3. 错误处理
```c
// 函数返回值检查
int result = sh367601b_init(&bms_device);
if (result != 0) {
    printf("Initialization failed: %d\n", result);
}

// 芯片索引有效性检查
if (chip_index >= bms_device.chip.data.chip_count) {
    printf("Invalid chip index: %d\n", chip_index);
}
```

## 注意事项

1. **硬件切换函数**: 必须为每个芯片注册硬件切换函数
2. **数据同步**: BMS数据只在最后一个芯片时更新
3. **任务流程**: 批量操作会自动管理芯片切换
4. **内存管理**: 注意数组边界检查
5. **线程安全**: 多线程环境需要加锁保护

---

**版本**: 2.0 | **日期**: 2025 | **模型**: Claude Sonnet 4
