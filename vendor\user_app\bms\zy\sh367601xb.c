#include "tl_common.h"
#include "sh367601xb.h"
#include "sh367601xb_config.h"
#include "sh367601xb_parser.h"
#include "sh367601xb_converter.h"
#include "sh367601xb_communication.h"
#include "sh367601xb_tool.h"
#include "sh367601xb_chip.h"
#include "../bms_data.h"




/* ==========================================BMS数据更新方法实现========================================== */
/**
 * @brief 处理ROM数据并更新到BMS系统配置
 * @param device 设备实例指针
 */
static void sh367601b_update_bms_from_rom(*********_Device* device)
{
    if (device == NULL) return;
    VoltageManager *vmgr = &device->bms_system.voltage_mgr;
    ProtectionParameterManager *pmgr = &device->bms_system.protection_param_mgr;

    /* 更新保护参数 */
    pmgr->methods.process_protection_data(pmgr, device->converter.method.ov_to_voltage(device->rom[0].ov), device->converter.method.ovr_to_voltage(device->rom[0].ovr), device->converter.method.ovt_to_value(device->rom[0].ovt),
    device->converter.method.uv_to_voltage(device->rom[0].uv), device->converter.method.uvr_to_voltage(device->rom[0].uvr), device->converter.method.uvt_to_value(device->rom[0].uvt),
    device->converter.method.ocd1v_to_voltage(device->rom[0].ocd1v), device->converter.method.ocd1t_to_value(device->rom[0].ocd1t),
    device->converter.method.ocd2v_to_voltage(device->rom[0].ocd2v), device->converter.method.ocd2t_to_value(device->rom[0].ocd2t),
    device->converter.method.occv_to_voltage(device->rom[0].occv), device->converter.method.occt_to_value(device->rom[0].occt),
    device->tool.method.calc_temp_from_adc(device->rom[0].otc), device->tool.method.calc_temp_from_adc(device->rom[0].otcr),
    device->tool.method.calc_low_temp_from_reg(device->rom[0].utc), device->tool.method.calc_low_temp_from_reg(device->rom[0].utcr),
    device->tool.method.calc_temp_from_adc(device->rom[0].otd), device->tool.method.calc_temp_from_adc(device->rom[0].otdr),
    device->tool.method.calc_low_temp_from_reg(device->rom[0].utd), device->tool.method.calc_low_temp_from_reg(device->rom[0].utdr),
    device->converter.method.bald_to_value(device->rom[0].bald), device->converter.method.balv_to_voltage(device->rom[0].balv));

    /* 更新电池串数 */
    vmgr->data.battery_count = device->rom[0].cn + 6;
    
    /* 根据芯片类型进行边界检查 */
    unsigned char max_cells = sh367601b_get_max_cells(device);
    if (vmgr->data.battery_count > max_cells) {
        vmgr->data.battery_count = max_cells;
        printf("*********: Battery count limited to %d cells for current chip type\n", max_cells);
    }
    
    printf("*********: BMS data updated from ROM\n");
}

/**
 * @brief 从********* RAM数据更新BMS系统（自动计算所有衍生值）
 * @param device 设备实例指针
 */
static void sh367601b_update_bms_from_ram(*********_Device* device)
{
    if (device == NULL) return;
    // AlarmManager *amgr = &device->bms_system.alarm_mgr;
    // /* 更新告警管理器数据 */
    // amgr->methods.process_alarm_data(amgr, device->ram.otc_flg, device->ram.utc_flg,
    // device->ram.otd_flg, device->ram.utd_flg, device->ram.occ_flg, device->ram.ocd1_flg, device->ram.ocd2_flg, 
    // device->ram.uv_flg, device->ram.ov_flg);

    // /* 更新状态管理器数据 */
    // StatusManager *smgr = &device->bms_system.status_mgr;
    // smgr->methods.process_status_data(smgr, device->ram.bal, device->ram.chg_fet,
    //                                  device->ram.dsg_fet, device->ram.chging, device->ram.dsging);

    // /* 更新电压管理器数据 */
    // VoltageManager *vmgr = &device->bms_system.voltage_mgr;
    // unsigned short cell_voltages[16] = {
    //     device->tool.reg_from_voltage(device->ram.cell1), device->tool.reg_from_voltage(device->ram.cell2), device->tool.reg_from_voltage(device->ram.cell3), device->tool.reg_from_voltage(device->ram.cell4),
    //     device->tool.reg_from_voltage(device->ram.cell5), device->tool.reg_from_voltage(device->ram.cell6), device->tool.reg_from_voltage(device->ram.cell7), device->tool.reg_from_voltage(device->ram.cell8),
    //     device->tool.reg_from_voltage(device->ram.cell9), device->tool.reg_from_voltage(device->ram.cell10), device->tool.reg_from_voltage(device->ram.cell11), device->tool.reg_from_voltage(device->ram.cell12),
    //     device->tool.reg_from_voltage(device->ram.cell13), device->tool.reg_from_voltage(device->ram.cell14), device->tool.reg_from_voltage(device->ram.cell15), device->tool.reg_from_voltage(device->ram.cell16)
    // };
    // vmgr->methods.process_voltage_data(vmgr, cell_voltages, vmgr->data.battery_count);

    // /* 更新电流管理器数据 */
    // CurrentManager *cmgr = &device->bms_system.current_mgr;
    // cmgr->methods.process_current_data(cmgr, device->tool.calc_current_from_adc(device->ram.cur, 1.0f, 90.0f, 26214.4f),
    //                                   device->ram.chging, device->ram.dsging);

    // /* 更新充放电管理器数据 */
    // ChargeDischargeManager *cdmgr = &device->bms_system.charge_discharge_mgr;
    // cdmgr->methods.process_charge_discharge_data(cdmgr, cmgr->data.total_current,
    //                                             device->ram.chging, device->ram.dsging, vmgr->data.total_voltage, device->bms_system.custom_param_mgr.data.battery_total_capacity);

    // /* 更新温度管理器数据 */
    // TemperatureManager *tmgr = &device->bms_system.temp_mgr;
    // signed char external_temps[4] = {
    //     device->tool.calc_external_temp(device->ram.temp1),
    //     device->tool.calc_external_temp(device->ram.temp2),
    //     device->tool.calc_external_temp(device->ram.temp3),
    //     0  // 预留第4个温度传感器
    // };
    // signed char chip_temp = device->tool.calc_external_temp(device->ram.tempn);
    // tmgr->methods.process_temperature_data(tmgr, external_temps, 3, chip_temp);

    // /* 更新电池状态管理器数据 */
    // BatteryStateManager *bsmgr = &device->bms_system.battery_state_mgr;
    // bsmgr->methods.process_battery_state_data(bsmgr, 0, cmgr->data.total_current, device->bms_system.custom_param_mgr.data.battery_total_capacity);
    printf("*********: BMS data update from RAM completed\n");
}


/* ==========================================设备初始化模块实现========================================== */

/**
 * @brief 初始化*********设备实例（自动初始化内嵌BMS系统）
 * @param device 设备指针
 * @return 0=成功，-1=失败
 */
int sh367601b_init(*********_Device* device)
{
    if (device == NULL) return -1;

    // /* 清零数据成员 */
    // memset(device->rom, 0, sizeof(device->rom));
    // memset(device->ram, 0, sizeof(device->ram));
    // memset(device->write_rom, 0, sizeof(device->write_rom));
    // memset(device->write_flags, 0, sizeof(device->write_flags));

    /* 初始化设备状态 */
    device->is_initialized = false;
    device->is_connected = false;
    device->chip.data.chip_count = 1;  /* 默认使用1个芯片 */
    device->chip.data.current_chip_index = 0;  /* 默认选中第0个芯片 */
    device->chip.data.write_rom_in_progress = 0;  /* 初始化写ROM流程状态 */
    device->chip.data.write_rom_target_chip_count = 0;  /* 初始化目标芯片数量 */
    
    /* 初始化所有芯片类型为默认值 */
    for (unsigned char i = 0; i < MAX_CHIP_COUNT; i++) {
        device->chip.data.chip_type[i] = *********_CHIP_16_SERIES;  /* 默认设置为16串芯片 */
    }
    /* 初始化配置管理模块方法指针 */
    device->config.method.set_id = sh367601b_config_set_id;
    device->config.method.set_enmosr = sh367601b_config_set_enmosr;
    device->config.method.set_chys = sh367601b_config_set_chys;
    device->config.method.set_tc = sh367601b_config_set_tc;
    device->config.method.set_cn = sh367601b_config_set_cn;
    device->config.method.set_bals = sh367601b_config_set_bals;
    device->config.method.set_chs = sh367601b_config_set_chs;
    device->config.method.set_ocra = sh367601b_config_set_ocra;
    device->config.method.set_eovr = sh367601b_config_set_eovr;
    device->config.method.set_euvr = sh367601b_config_set_euvr;
    device->config.method.set_eow = sh367601b_config_set_eow;
    device->config.method.set_eot3 = sh367601b_config_set_eot3;
    device->config.method.set_enmos = sh367601b_config_set_enmos;
    device->config.method.set_ovt = sh367601b_config_set_ovt;
    device->config.method.set_ov = sh367601b_config_set_ov;
    device->config.method.set_ovr = sh367601b_config_set_ovr;
    device->config.method.set_uvr = sh367601b_config_set_uvr;
    device->config.method.set_lov = sh367601b_config_set_lov;
    device->config.method.set_balt = sh367601b_config_set_balt;
    device->config.method.set_uvt = sh367601b_config_set_uvt;
    device->config.method.set_uv = sh367601b_config_set_uv;
    device->config.method.set_balv = sh367601b_config_set_balv;
    device->config.method.set_bald = sh367601b_config_set_bald;
    device->config.method.set_ocd1v = sh367601b_config_set_ocd1v;
    device->config.method.set_ocd1t = sh367601b_config_set_ocd1t;
    device->config.method.set_sct = sh367601b_config_set_sct;
    device->config.method.set_ocd2v = sh367601b_config_set_ocd2v;
    device->config.method.set_ocd2t = sh367601b_config_set_ocd2t;
    device->config.method.set_occv = sh367601b_config_set_occv;
    device->config.method.set_occt = sh367601b_config_set_occt;
    device->config.method.set_otc = sh367601b_config_set_otc;
    device->config.method.set_otcr = sh367601b_config_set_otcr;
    device->config.method.set_otd = sh367601b_config_set_otd;
    device->config.method.set_otdr = sh367601b_config_set_otdr;
    device->config.method.set_utc = sh367601b_config_set_utc;
    device->config.method.set_utcr = sh367601b_config_set_utcr;
    device->config.method.set_utd = sh367601b_config_set_utd;
    device->config.method.set_utdr = sh367601b_config_set_utdr;
    /* 注册ROM数据打包方法 */
    device->config.method.pack_rom_data = sh367601b_config_pack_rom_data;
    /* 注册ROM副本管理方法 */
    device->config.method.create_rom_copy = sh367601b_config_create_rom_copy;
    
    /* 初始化通信驱动模块方法指针 */
    device->comm.method.reset = sh367601b_comm_reset;
    device->comm.method.write_command = sh367601b_comm_write_command;
    device->comm.method.write_data = sh367601b_comm_write_data;
    device->comm.method.read_rom = sh367601b_comm_read_rom;
    device->comm.method.read_ram = sh367601b_comm_read_ram;

    /* 初始化数据解析模块方法指针 */
    device->parser.method.parse_rom = sh367601b_parser_parse_rom;
    device->parser.method.parse_ram = sh367601b_parser_parse_ram;
    device->parser.method.print_rom = sh367601b_parser_print_rom;
    device->parser.method.print_ram = sh367601b_parser_print_ram;

    /* 初始化数据转换模块方法指针 */
    device->converter.method.ov_to_voltage = sh367601b_converter_ov_to_voltage;
    device->converter.method.ovr_to_voltage = sh367601b_converter_ovr_to_voltage;
    device->converter.method.balv_to_voltage = sh367601b_converter_balv_to_voltage;
    device->converter.method.uv_to_voltage = sh367601b_converter_uv_to_voltage;
    device->converter.method.uvr_to_voltage = sh367601b_converter_uvr_to_voltage;
    device->converter.method.lov_to_voltage = sh367601b_converter_lov_to_voltage;
    device->converter.method.ocd1v_to_voltage = sh367601b_converter_ocd1v_to_voltage;
    device->converter.method.ocd2v_to_voltage = sh367601b_converter_ocd2v_to_voltage;
    device->converter.method.ocd2vd_to_voltage = sh367601b_converter_ocd2vd_to_voltage;
    device->converter.method.occv_to_voltage = sh367601b_converter_occv_to_voltage;
    device->converter.method.tc_to_delay = sh367601b_converter_tc_to_delay;

    /* 初始化延时转换函数指针 */
    device->converter.method.ovt_to_value = sh367601b_converter_ovt_to_value;
    device->converter.method.uvt_to_value = sh367601b_converter_uvt_to_value;
    device->converter.method.ocd1t_to_value = sh367601b_converter_ocd1t_to_value;
    device->converter.method.ocd2t_to_value = sh367601b_converter_ocd2t_to_value;
    device->converter.method.occt_to_value = sh367601b_converter_occt_to_value;
    device->converter.method.balt_to_value = sh367601b_converter_balt_to_value;
    device->converter.method.bald_to_value = sh367601b_converter_bald_to_value;

    /* 初始化工具函数模块方法指针 */
    device->tool.method.calc_temp_from_resistance = ntc_calculate_temp_from_resistance;
    device->tool.method.calc_temp_from_adc = ntc_calculate_temp_from_adc;
    device->tool.method.calc_low_temp_from_reg = ntc_calculate_low_temp_from_reg;
    device->tool.method.calc_external_temp = ntc_calculate_external_temp;
    device->tool.method.find_resistance_from_temp = ntc_find_resistance_from_temp;
    device->tool.method.calc_high_temp_reg = ntc_calculate_high_temp_reg;
    device->tool.method.calc_low_temp_reg = ntc_calculate_low_temp_reg;
    device->tool.method.calc_current_from_adc = current_calculate_from_adc;
    device->tool.method.hybrid_filter = hybridFilter;
    device->tool.method.reg_from_voltage = Reg_From_Voltage;

    /* 初始化BMS数据更新方法指针 */
    device->bms_sync.update_realtime_data = sh367601b_update_bms_from_ram;  /* RAM实时数据更新方法 */
    device->bms_sync.update_protection_config = sh367601b_update_bms_from_rom;  /* ROM保护配置更新方法 */

    /* 初始化芯片管理模块方法指针 */
    device->chip.method.switch_to_chip = sh367601b_switch_to_chip;
    device->chip.method.write_rom_for_chip = sh367601b_write_rom_for_chip;
    device->chip.method.set_chip_count = sh367601b_set_chip_count;
    device->chip.method.start_batch_write_rom = sh367601b_start_batch_write_rom;
    

    device->is_initialized = true;
    printf("SH367601XB: Device initialized with BMS system integration\n");
    return 0;
}

