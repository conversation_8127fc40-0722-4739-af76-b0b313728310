#include "tl_common.h"
#include "sh367601xb.h"
#include "sh367601xb_config.h"
#include "sh367601xb_parser.h"
#include "sh367601xb_converter.h"
#include "sh367601xb_communication.h"
#include "sh367601xb_tool.h"
#include "sh367601xb_chip.h"
#include "../bms_data.h"




/* ==========================================BMS数据更新方法实现========================================== */
/**
 * @brief 处理ROM数据并更新到BMS系统配置
 * @param device 设备实例指针
 */
static void sh367601b_update_bms_from_rom(*********_Device* device)
{
    if (device == NULL) return;
    VoltageManager *vmgr = &device->bms_system.voltage_mgr;
    ProtectionParameterManager *pmgr = &device->bms_system.protection_param_mgr;

    /* 更新保护参数 */
    pmgr->methods.process_protection_data(pmgr, device->converter.method.ov_to_voltage(device->rom[0].ov), device->converter.method.ovr_to_voltage(device->rom[0].ovr), device->converter.method.ovt_to_value(device->rom[0].ovt),
    device->converter.method.uv_to_voltage(device->rom[0].uv), device->converter.method.uvr_to_voltage(device->rom[0].uvr), device->converter.method.uvt_to_value(device->rom[0].uvt),
    device->converter.method.ocd1v_to_voltage(device->rom[0].ocd1v), device->converter.method.ocd1t_to_value(device->rom[0].ocd1t),
    device->converter.method.ocd2v_to_voltage(device->rom[0].ocd2v), device->converter.method.ocd2t_to_value(device->rom[0].ocd2t),
    device->converter.method.occv_to_voltage(device->rom[0].occv), device->converter.method.occt_to_value(device->rom[0].occt),
    device->tool.method.calc_temp_from_adc(device->rom[0].otc), device->tool.method.calc_temp_from_adc(device->rom[0].otcr),
    device->tool.method.calc_low_temp_from_reg(device->rom[0].utc), device->tool.method.calc_low_temp_from_reg(device->rom[0].utcr),
    device->tool.method.calc_temp_from_adc(device->rom[0].otd), device->tool.method.calc_temp_from_adc(device->rom[0].otdr),
    device->tool.method.calc_low_temp_from_reg(device->rom[0].utd), device->tool.method.calc_low_temp_from_reg(device->rom[0].utdr),
    device->converter.method.bald_to_value(device->rom[0].bald), device->converter.method.balv_to_voltage(device->rom[0].balv));

    /* 更新电池串数 */
    vmgr->data.battery_count = device->rom[0].cn + 6;
    
    /* 根据芯片类型进行边界检查 */
    unsigned char max_cells = device->chip.method.get_chip_max_cells(device, device->chip.data.current_chip_index);
    if (vmgr->data.battery_count > max_cells) {
        vmgr->data.battery_count = max_cells;
    }
    printf("*********: Battery count %d\n", vmgr->data.battery_count);
}

/**
 * @brief 从RAM数据更新BMS数据管理系统（只在最后一个芯片时更新）
 * @param device 设备实例指针
 * @note 只有当前芯片是最后一个芯片时，才进行BMS数据更新
 */
static void sh367601b_update_bms_from_ram(*********_Device* device)
{
    if (device == NULL) return;
    
    /* 检查是否为最后一个芯片 */
    unsigned char last_chip_index = device->chip.data.chip_count - 1;
    if (device->chip.data.current_chip_index != last_chip_index) {
        printf("*********: Skip BMS update for chip %d (not last chip)\n", device->chip.data.current_chip_index);
        return;
    }
    
    /* 收集所有芯片的告警和状态数据并进行|运算（0=正常，1=异常） */
    unsigned char combined_flags[14] = {0}; /* 存储所有合并后的标志位 */
    for (unsigned char chip_idx = 0; chip_idx < device->chip.data.chip_count; chip_idx++) {
        combined_flags[0] |= device->ram[chip_idx].otc_flg;   /* 充电过温告警 */
        combined_flags[1] |= device->ram[chip_idx].utc_flg;   /* 充电欠温告警 */
        combined_flags[2] |= device->ram[chip_idx].otd_flg;   /* 放电过温告警 */
        combined_flags[3] |= device->ram[chip_idx].utd_flg;   /* 放电欠温告警 */
        combined_flags[4] |= device->ram[chip_idx].occ_flg;   /* 充电过流告警 */
        combined_flags[5] |= device->ram[chip_idx].ocd1_flg;  /* 放电过流1告警 */
        combined_flags[6] |= device->ram[chip_idx].ocd2_flg;  /* 放电过流2告警 */
        combined_flags[7] |= device->ram[chip_idx].uv_flg;    /* 欠压告警 */
        combined_flags[8] |= device->ram[chip_idx].ov_flg;    /* 过压告警 */
        combined_flags[9] |= device->ram[chip_idx].bal;       /* 均衡状态 */
        combined_flags[10] |= device->ram[chip_idx].chg_fet;  /* 充电FET状态 */
        combined_flags[11] |= device->ram[chip_idx].dsg_fet;  /* 放电FET状态 */
        combined_flags[12] |= device->ram[chip_idx].chging;   /* 充电状态 */
        combined_flags[13] |= device->ram[chip_idx].dsging;   /* 放电状态 */
    }
    AlarmManager *amgr = &device->bms_system.alarm_mgr;
    /* 更新告警管理器数据 */
    amgr->methods.process_alarm_data(amgr, combined_flags[0], combined_flags[1],
    combined_flags[2], combined_flags[3], combined_flags[4], combined_flags[5], combined_flags[6], 
    combined_flags[7], combined_flags[8]);
    /* 更新状态管理器数据 */
    StatusManager *smgr = &device->bms_system.status_mgr;
    smgr->methods.process_status_data(smgr, combined_flags[9], combined_flags[10],
                                     combined_flags[11], combined_flags[12], combined_flags[13]);


    /* 收集所有芯片的电压数据，进行电压更新 */
    unsigned short all_cell_voltages[MAX_CHIP_COUNT * 16];  /* 最大支持4个芯片，每个16串 */
    unsigned int total_cell_count = 0;
    /* 遍历所有芯片，收集电压数据 */
    for (unsigned char chip_idx = 0; chip_idx < device->chip.data.chip_count; chip_idx++) 
    {
        /* 获取当前芯片实际配置的串数（从ROM配置中获取）*/
        unsigned char actual_cell_count = device->bms_system.voltage_mgr.data.battery_count;  /* cn寄存器值+6为实际串数 */
        unsigned char chip_max_cells = device->chip.method.get_chip_max_cells(device, device->chip.data.chip_type[chip_idx]);
        
        /* 确保不超过芯片的最大串数 */
        if (actual_cell_count > chip_max_cells) actual_cell_count = chip_max_cells;
        
        printf("*********: Chip %d - collecting %d cells (max: %d)\n", chip_idx, actual_cell_count, chip_max_cells);
        
        /* 根据实际串数收集电压数据 */
        for (unsigned char cell_idx = 1; cell_idx <= actual_cell_count; cell_idx++) 
        {
            unsigned short cell_voltage = 0;
            switch (cell_idx) {
                case 1: cell_voltage = device->tool.method.reg_from_voltage(device->ram[chip_idx].cell1); break;
                case 2: cell_voltage = device->tool.method.reg_from_voltage(device->ram[chip_idx].cell2); break;
                case 3: cell_voltage = device->tool.method.reg_from_voltage(device->ram[chip_idx].cell3); break;
                case 4: cell_voltage = device->tool.method.reg_from_voltage(device->ram[chip_idx].cell4); break;
                case 5: cell_voltage = device->tool.method.reg_from_voltage(device->ram[chip_idx].cell5); break;
                case 6: cell_voltage = device->tool.method.reg_from_voltage(device->ram[chip_idx].cell6); break;
                case 7: cell_voltage = device->tool.method.reg_from_voltage(device->ram[chip_idx].cell7); break;
                case 8: cell_voltage = device->tool.method.reg_from_voltage(device->ram[chip_idx].cell8); break;
                case 9: cell_voltage = device->tool.method.reg_from_voltage(device->ram[chip_idx].cell9); break;
                case 10: cell_voltage = device->tool.method.reg_from_voltage(device->ram[chip_idx].cell10); break;
                case 11: cell_voltage = device->tool.method.reg_from_voltage(device->ram[chip_idx].cell11); break;
                case 12: cell_voltage = device->tool.method.reg_from_voltage(device->ram[chip_idx].cell12); break;
                case 13: cell_voltage = device->tool.method.reg_from_voltage(device->ram[chip_idx].cell13); break;
                case 14: cell_voltage = device->tool.method.reg_from_voltage(device->ram[chip_idx].cell14); break;
                case 15: cell_voltage = device->tool.method.reg_from_voltage(device->ram[chip_idx].cell15); break;
                case 16: cell_voltage = device->tool.method.reg_from_voltage(device->ram[chip_idx].cell16); break;
                default: continue;
            }
            all_cell_voltages[total_cell_count++] = cell_voltage;
        }
    }
    /* 更新电压管理器数据（使用所有芯片的电压数据） */
    VoltageManager *vmgr = &device->bms_system.voltage_mgr;
    vmgr->methods.process_voltage_data(vmgr, all_cell_voltages, total_cell_count);

    /* 更新电流管理器数据 */
    CurrentManager *cmgr = &device->bms_system.current_mgr;
    cmgr->methods.process_current_data(cmgr, device->tool.method.calc_current_from_adc(device->ram[0].cur, 1.0f),
                                      device->ram[0].chging, device->ram[0].dsging);

    /* 更新充放电管理器数据 */
    ChargeDischargeManager *cdmgr = &device->bms_system.charge_discharge_mgr;
    cdmgr->methods.process_charge_discharge_data(cdmgr, cmgr->data.total_current,
                                                device->ram[0].chging, device->ram[0].dsging, vmgr->data.total_voltage, device->bms_system.custom_param_mgr.data.battery_total_capacity);

    /* 更新温度管理器数据 */
    TemperatureManager *tmgr = &device->bms_system.temp_mgr;
    signed char external_temps[4] = {
        device->tool.method.calc_external_temp(device->ram[0].temp1),
        device->tool.method.calc_external_temp(device->ram[0].temp2),
        device->tool.method.calc_external_temp(device->ram[0].temp3),
    };
    signed char chip_temp = device->tool.method.calc_external_temp(device->ram[0].tempn);
    tmgr->methods.process_temperature_data(tmgr, external_temps, 3, chip_temp);

    /* 更新电池状态管理器数据 */
    BatteryStateManager *bsmgr = &device->bms_system.battery_state_mgr;
    bsmgr->methods.process_battery_state_data(bsmgr, 0, cmgr->data.total_current, device->bms_system.custom_param_mgr.data.battery_total_capacity);
    
    printf("*********: BMS data update completed - Total cells: %d from %d chips\n", total_cell_count, device->chip.data.chip_count);
}



/* ==========================================设备初始化模块实现========================================== */
/**
 * @brief 初始化*********设备实例（自动初始化内嵌BMS系统）
 * @param device 设备指针
 * @return 0=成功，-1=失败
 */
int sh367601b_init(*********_Device* device)
{
    if (device == NULL) return -1;

    // /* 清零数据成员 */
    // memset(device->rom, 0, sizeof(device->rom));
    // memset(device->ram, 0, sizeof(device->ram));
    // memset(device->write_rom, 0, sizeof(device->write_rom));
    // memset(device->write_flags, 0, sizeof(device->write_flags));
    bms_init(&device->bms_system);
    /* 初始化设备状态 */
    device->is_initialized = false;
    device->is_connected = false;
    device->chip.data.chip_count = 1;  /* 默认使用1个芯片 */
    device->chip.data.current_chip_index = 0;  /* 默认选中第0个芯片 */
    device->chip.data.write_rom_in_progress = 0;  /* 初始化写ROM流程状态 */
    device->chip.data.write_rom_target_chip_count = 0;  /* 初始化目标芯片数量 */
    
    /* 初始化所有芯片类型为默认值 */
    for (unsigned char i = 0; i < MAX_CHIP_COUNT; i++) 
    {
        device->chip.data.chip_type[i] = *********_CHIP_16_SERIES;  /* 默认设置为16串芯片 */
        device->chip.data.chip_switch_functions[i] = NULL;          /* 初始化芯片切换函数为空 */
    }
    /* 初始化配置管理模块方法指针 */
    device->config.method.set_id = sh367601b_config_set_id;
    device->config.method.set_enmosr = sh367601b_config_set_enmosr;
    device->config.method.set_chys = sh367601b_config_set_chys;
    device->config.method.set_tc = sh367601b_config_set_tc;
    device->config.method.set_cn = sh367601b_config_set_cn;
    device->config.method.set_bals = sh367601b_config_set_bals;
    device->config.method.set_chs = sh367601b_config_set_chs;
    device->config.method.set_ocra = sh367601b_config_set_ocra;
    device->config.method.set_eovr = sh367601b_config_set_eovr;
    device->config.method.set_euvr = sh367601b_config_set_euvr;
    device->config.method.set_eow = sh367601b_config_set_eow;
    device->config.method.set_eot3 = sh367601b_config_set_eot3;
    device->config.method.set_enmos = sh367601b_config_set_enmos;
    device->config.method.set_ovt = sh367601b_config_set_ovt;
    device->config.method.set_ov = sh367601b_config_set_ov;
    device->config.method.set_ovr = sh367601b_config_set_ovr;
    device->config.method.set_uvr = sh367601b_config_set_uvr;
    device->config.method.set_lov = sh367601b_config_set_lov;
    device->config.method.set_balt = sh367601b_config_set_balt;
    device->config.method.set_uvt = sh367601b_config_set_uvt;
    device->config.method.set_uv = sh367601b_config_set_uv;
    device->config.method.set_balv = sh367601b_config_set_balv;
    device->config.method.set_bald = sh367601b_config_set_bald;
    device->config.method.set_ocd1v = sh367601b_config_set_ocd1v;
    device->config.method.set_ocd1t = sh367601b_config_set_ocd1t;
    device->config.method.set_sct = sh367601b_config_set_sct;
    device->config.method.set_ocd2v = sh367601b_config_set_ocd2v;
    device->config.method.set_ocd2t = sh367601b_config_set_ocd2t;
    device->config.method.set_occv = sh367601b_config_set_occv;
    device->config.method.set_occt = sh367601b_config_set_occt;
    device->config.method.set_otc = sh367601b_config_set_otc;
    device->config.method.set_otcr = sh367601b_config_set_otcr;
    device->config.method.set_otd = sh367601b_config_set_otd;
    device->config.method.set_otdr = sh367601b_config_set_otdr;
    device->config.method.set_utc = sh367601b_config_set_utc;
    device->config.method.set_utcr = sh367601b_config_set_utcr;
    device->config.method.set_utd = sh367601b_config_set_utd;
    device->config.method.set_utdr = sh367601b_config_set_utdr;
    /* 注册ROM数据打包方法 */
    device->config.method.pack_rom_data = sh367601b_config_pack_rom_data;
    /* 注册ROM副本管理方法 */
    device->config.method.create_rom_copy = sh367601b_config_create_rom_copy;
    
    /* 初始化通信驱动模块方法指针 */
    device->comm.method.reset = sh367601b_comm_reset;
    device->comm.method.write_command = sh367601b_comm_write_command;
    device->comm.method.write_data = sh367601b_comm_write_data;
    device->comm.method.read_rom = sh367601b_comm_read_rom;
    device->comm.method.read_ram = sh367601b_comm_read_ram;

    /* 初始化数据解析模块方法指针 */
    device->parser.method.parse_rom = sh367601b_parser_parse_rom;
    device->parser.method.parse_ram = sh367601b_parser_parse_ram;
    device->parser.method.print_rom = sh367601b_parser_print_rom;
    device->parser.method.print_ram = sh367601b_parser_print_ram;

    /* 初始化数据转换模块方法指针 */
    device->converter.method.ov_to_voltage = sh367601b_converter_ov_to_voltage;
    device->converter.method.ovr_to_voltage = sh367601b_converter_ovr_to_voltage;
    device->converter.method.balv_to_voltage = sh367601b_converter_balv_to_voltage;
    device->converter.method.uv_to_voltage = sh367601b_converter_uv_to_voltage;
    device->converter.method.uvr_to_voltage = sh367601b_converter_uvr_to_voltage;
    device->converter.method.lov_to_voltage = sh367601b_converter_lov_to_voltage;
    device->converter.method.ocd1v_to_voltage = sh367601b_converter_ocd1v_to_voltage;
    device->converter.method.ocd2v_to_voltage = sh367601b_converter_ocd2v_to_voltage;
    device->converter.method.ocd2vd_to_voltage = sh367601b_converter_ocd2vd_to_voltage;
    device->converter.method.occv_to_voltage = sh367601b_converter_occv_to_voltage;
    device->converter.method.tc_to_delay = sh367601b_converter_tc_to_delay;

    /* 初始化延时转换函数指针 */
    device->converter.method.ovt_to_value = sh367601b_converter_ovt_to_value;
    device->converter.method.uvt_to_value = sh367601b_converter_uvt_to_value;
    device->converter.method.ocd1t_to_value = sh367601b_converter_ocd1t_to_value;
    device->converter.method.ocd2t_to_value = sh367601b_converter_ocd2t_to_value;
    device->converter.method.occt_to_value = sh367601b_converter_occt_to_value;
    device->converter.method.balt_to_value = sh367601b_converter_balt_to_value;
    device->converter.method.bald_to_value = sh367601b_converter_bald_to_value;

    /* 初始化工具函数模块方法指针 */
    device->tool.method.calc_temp_from_resistance = ntc_calculate_temp_from_resistance;
    device->tool.method.calc_temp_from_adc = ntc_calculate_temp_from_adc;
    device->tool.method.calc_low_temp_from_reg = ntc_calculate_low_temp_from_reg;
    device->tool.method.calc_external_temp = ntc_calculate_external_temp;
    device->tool.method.find_resistance_from_temp = ntc_find_resistance_from_temp;
    device->tool.method.calc_high_temp_reg = ntc_calculate_high_temp_reg;
    device->tool.method.calc_low_temp_reg = ntc_calculate_low_temp_reg;
    device->tool.method.calc_current_from_adc = current_calculate_from_adc;
    device->tool.method.hybrid_filter = hybridFilter;
    device->tool.method.reg_from_voltage = Reg_From_Voltage;

    /* 初始化BMS数据更新方法指针 */
    device->bms_sync.update_realtime_data = sh367601b_update_bms_from_ram;  /* RAM实时数据更新方法 */
    device->bms_sync.update_protection_config = sh367601b_update_bms_from_rom;  /* ROM保护配置更新方法 */

    /* 初始化芯片管理模块方法指针 */
    device->chip.method.switch_to_chip = sh36760_switch_to_chip;
    device->chip.method.write_rom_for_chip = sh36760_write_rom_for_chip;
    device->chip.method.set_chip_count = sh36760_set_chip_count;
    device->chip.method.start_batch_write_rom = sh36760_start_batch_write_rom;
    device->chip.method.set_chip_type = sh36760_set_chip_type;
    device->chip.method.get_chip_type = sh36760_get_chip_type;
    device->chip.method.get_chip_max_cells = sh36760_get_chip_max_cells;
    device->chip.method.handle_reset_task_chip_switching = sh36760_handle_reset_task_chip_switching;
    device->chip.method.handle_ram_read_chip_switching = sh36760_handle_ram_read_chip_switching;
    device->chip.method.set_chip_switch_function = sh36760_set_chip_switch_function;
    
    device->is_initialized = true;
    printf("SH367601XB: Device initialized with BMS system integration\n");
    return 0;
}

