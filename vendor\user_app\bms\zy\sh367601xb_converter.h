#ifndef SH367601XB_CONVERTER_H
#define SH367601XB_CONVERTER_H

/* ==========================================数据转换模块声明========================================== */

/**
 * @brief 过充电保护电压寄存器值转换为实际电压
 */
extern unsigned short sh367601b_converter_ov_to_voltage(unsigned short ov);

/**
 * @brief 过充电恢复电压寄存器值转换为实际电压
 */
extern unsigned short sh367601b_converter_ovr_to_voltage(unsigned short ovr);

/**
 * @brief 过充电保护延时寄存器值转换
 */
extern unsigned short sh367601b_converter_ovt_to_value(unsigned char ovt);

/**
 * @brief 过放电保护延时寄存器值转换
 */
extern unsigned short sh367601b_converter_uvt_to_value(unsigned char uvt);

/**
 * @brief 放电过流1保护延时寄存器值转换
 */
extern unsigned short sh367601b_converter_ocd1t_to_value(unsigned char ocd1t);

/**
 * @brief 放电过流2保护延时寄存器值转换
 */
extern unsigned short sh367601b_converter_ocd2t_to_value(unsigned char ocd2t);

/**
 * @brief 充电过流保护延时寄存器值转换
 */
extern unsigned short sh367601b_converter_occt_to_value(unsigned char occt);

/**
 * @brief 均衡进入延时寄存器值转换
 */
extern unsigned short sh367601b_converter_balt_to_value(unsigned char balt);

/**
 * @brief 均衡开启压差转换
 */
extern unsigned short sh367601b_converter_bald_to_value(unsigned char bald);

/**
 * @brief 均衡开启电压寄存器值转换为实际电压
 */
extern unsigned short sh367601b_converter_balv_to_voltage(unsigned short balv);

/**
 * @brief 过放电保护电压寄存器值转换为实际电压
 */
extern unsigned short sh367601b_converter_uv_to_voltage(unsigned short uv);

/**
 * @brief 过放电恢复电压寄存器值转换为实际电压
 */
extern unsigned short sh367601b_converter_uvr_to_voltage(unsigned short uvr);

/**
 * @brief 低压禁止充电电压寄存器值转换为实际电压
 */
extern unsigned short sh367601b_converter_lov_to_voltage(unsigned short lov);

/**
 * @brief 放电过流1保护电压寄存器值转换为实际电压
 */
extern unsigned short sh367601b_converter_ocd1v_to_voltage(unsigned short ocd1v);

/**
 * @brief 放电过流2保护电压寄存器值转换为实际电压
 */
extern unsigned short sh367601b_converter_ocd2v_to_voltage(unsigned short ocd2v);

/**
 * @brief 短路保护电压寄存器值转换为实际电压
 */
extern unsigned short sh367601b_converter_ocd2vd_to_voltage(unsigned short ocd2vd);

/**
 * @brief 充电过流保护电压寄存器值转换为实际电压
 */
extern unsigned short sh367601b_converter_occv_to_voltage(unsigned short occv);

/**
 * @brief 充电温度保护延时寄存器值转换为实际延时
 */
extern unsigned char sh367601b_converter_tc_to_delay(char tc);

#endif /* SH367601XB_CONVERTER_H */
