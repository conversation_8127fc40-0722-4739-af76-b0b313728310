{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "BMS_Base", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "825x_ble_sample::@6890427a1f51a3e7e1df", "jsonFile": "target-825x_ble_sample-Debug-d19f4b14c2aea4713355.json", "name": "825x_ble_sample", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/Telink_Project/BMS_Base/cmake_build", "source": "D:/Telink_Project/BMS_Base"}, "version": {"major": 2, "minor": 8}}