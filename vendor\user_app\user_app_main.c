#include "user_app_main.h"
#include "bms/zy/sh367601xb.h"
#include "bms/zy/sh367601xb_tool.h"
#include "bms/zy/sh367601xb_chip.h"  /* 添加芯片管理模块头文件 */

#include "flash/user_app_flash.h"
#include "agreement/agreement.h"

#define USER_APP_ZERO_COUNT 10        /* 零点采样次数 */
/* 队列 */
Queue q;
static __attribute__((used)) QueueNode node;
static __attribute__((used)) int read_time;
static __attribute__((used)) char sh367601x_task_lock;
/* 全局数据 */
User_App_Ble_TypeDef  Ble;
User_App_Uart_TypeDef Uart;

*********_Device sh367601xb;  


#define USER_APP_RS2058_IO1 GPIO_PB6

/* 初始化引脚RS2058 */
void User_App_Rs2058_Init(void)
{
    gpio_set_func(USER_APP_RS2058_IO1, AS_GPIO); 		/* 数字gpio */
	gpio_set_output_en(USER_APP_RS2058_IO1, 1);		/* 使能输出 */
	gpio_set_input_en(USER_APP_RS2058_IO1, 0); 		/* 禁用输入 */
    gpio_write(USER_APP_RS2058_IO1, 0); 
}
/* 拉高 */
void User_App_Rs2058_Gpio1_Hight(void)
{
    gpio_write(USER_APP_RS2058_IO1, 1); 				/* 输出高 */
    printf("io1 hight\n");
}
/* 拉低 */
void User_App_Rs2058_Gpio1_Low(void)
{
    gpio_write(USER_APP_RS2058_IO1, 0); 				/* 输出低 */
    printf("io1 low\n");
}

/* 用户app初始化 */
void User_App_Init(void)
{
    /* 分路串口开关初始化 */
    User_App_Rs2058_Init();

    /* 初始化*********设备 */
    sh367601b_init(&sh367601xb);
    /* 设置芯片数量 */
    sh367601xb.chip.method.set_chip_count(&sh367601xb, 2);
    /* 设置芯片切换函数 */
    sh367601xb.chip.method.set_chip_switch_function(&sh367601xb, 0, User_App_Rs2058_Gpio1_Low);
    sh367601xb.chip.method.set_chip_switch_function(&sh367601xb, 1, User_App_Rs2058_Gpio1_Hight);   
    /* 设置单个芯片类型 */
    sh367601xb.chip.method.set_chip_type(&sh367601xb, 0, *********_CHIP_10_SERIES);
    sh367601xb.chip.method.set_chip_type(&sh367601xb, 1, *********_CHIP_16_SERIES);

    /* 添加开机任务 */
    queue_push(&q, QUEUE_RESET_TASK, QUEUE_RESET_TIME);
    queue_push(&q, QUEUE_ROM_TASK, QUEUE_ROM_TIME);
    
    /* 定时器初始化 */
    timer0_set_mode(TIMER_MODE_SYSCLK,0, 20 * CLOCK_SYS_CLOCK_1MS);
    timer1_set_mode(TIMER_MODE_SYSCLK,0, 2000 * CLOCK_SYS_CLOCK_1MS);
	timer_start(TIMER0);
	timer_start(TIMER1);
    
    printf("Multi-Chip BMS System Init Completed\n");
}
/* 定时器回调函数 */
void time0_callback(void)
{
    if (sh367601x_task_lock) return;
    queue_push(&q, QUEUE_CURR_TASK, QUEUE_CURR_TIME);
}
void time1_callback(void)
{
    queue_push(&q, QUEUE_RAM_TASK, QUEUE_RAM_TIME);
}



/* 蓝牙数据处理 */
void User_App_Sh367601x_Ble_process(unsigned char *data, unsigned char len)
{
    /* 通过协议处理蓝牙数据 */
    protocol_process_data(data, len);
}
/* 串口数据处理 */
void User_App_Sh367601x_Uart_process(unsigned char *data, unsigned char len)
{
    if (0x5A == data[2])
    {
        switch (data[1])
        {
            /* 写使能 */
            case 0x0A:
            {
                printf("write cmd successful\n");
                break;
            }
            /* 写命令 */
            case 0x01:
            {
                printf("write successful\n");
                break;
            }
            /* 读rom */
            case 0x02:
            {
                /* 解析rom */
                sh367601xb.parser.method.parse_rom(&sh367601xb.rom[sh367601xb.chip.data.current_chip_index], &data[5]);
                sh367601xb.bms_sync.update_protection_config(&sh367601xb);
                sh367601xb.chip.method.handle_ram_read_chip_switching(&sh367601xb, &q, true);
                break;
            }
            /* 读ram */
            case 0x03:
            {
                /* 处理电流 */
                if (0x02 == data[4])
                {
                    /* 直接更新芯片RAM数据 */
                    sh367601xb.ram[0].cur = (data[5] << 8) | data[6];
                    int current = sh367601xb.tool.method.calc_current_from_adc(sh367601xb.ram[0].cur, 1.0f);   
                    sh367601xb.bms_system.current_mgr.methods.process_current_data(&sh367601xb.bms_system.current_mgr, current,sh367601xb.ram[0].chging, sh367601xb.ram[0].dsging);
                    sh367601xb.bms_system.battery_state_mgr.methods.process_battery_state_data(&sh367601xb.bms_system.battery_state_mgr, 0, current, sh367601xb.bms_system.custom_param_mgr.data.battery_total_capacity);
                }
                else
                {
                    /* 解析ram */
                    sh367601xb.parser.method.parse_ram(&sh367601xb.ram[sh367601xb.chip.data.current_chip_index], &data[5]);
                    /* RAM读取完成后，检查是否需要切换到下一个芯片 */
                    sh367601xb.chip.method.handle_ram_read_chip_switching(&sh367601xb, &q, false);
                }
                break;
            }
            /* 软件复位 */
            case 0x0B:
            {
                printf("reset\n");
                break;
            }
            default: break;
        }
    }
}



/* 数据处理 */
void User_App_Logic(void)
{
    /* 处理蓝牙数据 */
    if (Ble.flg)
    {
        Ble.flg = false;
        User_App_Sh367601x_Ble_process(Ble.buff, Ble.len);
    }
    /* 处理串口数据 */
    if (Uart.flg)
    {
        Uart.flg = false;
        User_App_Sh367601x_Uart_process(Uart.buff, Uart.len);
    }
    
    /* 队列 */
    if (clock_time_exceed(read_time,  node.time * 1000))
    {
        /* 解锁 */
        sh367601x_task_lock = false;
        node.time = 0;
        if (!queue_empty(&q))
        {
            /* 加锁 */
            sh367601x_task_lock = true;
            queue_pop(&q, &node);
            switch (node.data)
            {
                case QUEUE_CURR_TASK:
                {
                    sh367601xb.comm.method.read_ram(RAM_CURRENT_ADDR_START, RAM_CURRENT_ADDR_LEN);
                    break;
                }
                case QUEUE_RAM_TASK:
                {
                    sh367601xb.comm.method.read_ram(RAM_ADDR_START, RAM_ADDR_LEN);
                    break;
                }
                case QUEUE_ROM_TASK:
                {
                    sh367601xb.comm.method.read_rom(ROM_ADDR_START, ROM_ADDR_LEN);
                    break;
                }
                case QUEUE_WRITE_ENABLE_TASK:
                { 
                    sh367601xb.comm.method.write_command();
                    break;
                }
                /* 通用ROM写入任务处理 - 处理所有QUEUE_WRITE_XX_TASK */
                case QUEUE_WRITE_01_TASK: case QUEUE_WRITE_02_TASK: case QUEUE_WRITE_03_TASK:
                case QUEUE_WRITE_04_TASK: case QUEUE_WRITE_05_TASK: case QUEUE_WRITE_06_TASK:
                case QUEUE_WRITE_07_TASK: case QUEUE_WRITE_08_TASK: case QUEUE_WRITE_09_TASK:
                case QUEUE_WRITE_0A_TASK: case QUEUE_WRITE_0B_TASK: case QUEUE_WRITE_0C_TASK:
                case QUEUE_WRITE_0D_TASK: case QUEUE_WRITE_0E_TASK: case QUEUE_WRITE_0F_TASK:
                case QUEUE_WRITE_10_TASK: case QUEUE_WRITE_11_TASK: case QUEUE_WRITE_12_TASK:
                case QUEUE_WRITE_13_TASK: case QUEUE_WRITE_14_TASK: case QUEUE_WRITE_15_TASK:
                {

                    break;
                }
                case QUEUE_RESET_TASK:
                {
                    /* 使用封装的芯片切换处理函数 */
                    sh367601xb.chip.method.handle_reset_task_chip_switching(&sh367601xb, &q);
                    sh367601xb.comm.method.reset();
                    break;
                }
                default: break;
            }
        }
        read_time = clock_time();
    }
}

