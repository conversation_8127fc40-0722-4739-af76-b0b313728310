#include "user_app_main.h"
#include "bms/zy/sh367601xb.h"
#include "bms/zy/sh367601xb_tool.h"
#include "bms/zy/sh367601xb_chip.h"  /* 添加芯片管理模块头文件 */

#include "flash/user_app_flash.h"
#include "agreement/agreement.h"

#define USER_APP_ZERO_COUNT 10        /* 零点采样次数 */
/* 队列 */
Queue q;
static __attribute__((used)) QueueNode node;
static __attribute__((used)) int read_time;
static __attribute__((used)) char sh367601x_task_lock;
/* 全局数据 */
User_App_Ble_TypeDef  Ble;
User_App_Uart_TypeDef Uart;

*********_Device sh3676010b;  


/* 用户app初始化 */
void User_App_Init(void)
{
    /* 分路串口开关初始化 */
    // User_App_Rs2058_Init();

    /* 初始化*********设备 */
    sh367601b_init(&sh3676010b);

    /* 添加开机任务 */
    queue_push(&q, QUEUE_RESET_TASK, QUEUE_RESET_TIME);
    queue_push(&q, QUEUE_ROM_TASK, QUEUE_ROM_TIME);
    
    /* 定时器初始化 */
    timer0_set_mode(TIMER_MODE_SYSCLK,0, 20 * CLOCK_SYS_CLOCK_1MS);
    timer1_set_mode(TIMER_MODE_SYSCLK,0, 2000 * CLOCK_SYS_CLOCK_1MS);
	timer_start(TIMER0);
	timer_start(TIMER1);
    
    printf("Multi-Chip BMS System Init Completed\n");

}
/* 定时器回调函数 */
void time0_callback(void)
{
    if (sh367601x_task_lock) return;
    queue_push(&q, QUEUE_CURR_TASK, QUEUE_CURR_TIME);
}

void time1_callback(void)
{
    queue_push(&q, QUEUE_RAM_TASK, QUEUE_RAM_TIME);
}



/* 蓝牙数据处理 */
void User_App_Sh367601x_Ble_process(unsigned char *data, unsigned char len)
{
    /* 通过协议处理蓝牙数据 */
    protocol_process_data(data, len);
}
/* 串口数据处理 */
void User_App_Sh367601x_Uart_process(unsigned char *data, unsigned char len)
{
    if (0x5A == data[2])
    {
        switch (data[1])
        {
            /* 写使能 */
            case 0x0A:
            {
                printf("write cmd successful\n");
                break;
            }
            /* 写命令 */
            case 0x01:
            {
                printf("write successful\n");
                break;
            }
            /* 读rom */
            case 0x02:
            {
                /* 解析rom */
                sh3676010b.parser.method.parse_rom(&sh3676010b, &data[5]);
                sh3676010b.bms_sync.update_protection_config(&sh3676010b);
                break;
            }
            /* 读ram */
            case 0x03:
            {
                /* 处理电流 */
                if (0x02 == data[4])
                {
                    /* 直接更新芯片RAM数据 */
                    sh3676010b.ram[0].cur = (data[5] << 8) | data[6];
                    sh3676010b.bms_system.current_mgr.methods.process_current_data(&sh3676010b.bms_system.current_mgr, sh3676010b.tool.method.calc_current_from_adc(sh3676010b.ram[0].cur, 1.0f, 90.0f, 26214.4f),
                                      sh3676010b.ram[0].chging, sh3676010b.ram[0].dsging);
                }
                else
                {
                    /* 解析ram */
                    sh3676010b.parser.method.parse_ram(&sh3676010b, &data[5]);
                }
                break;
            }
            /* 软件复位 */
            case 0x0B:
            {
                printf("reset\n");
                break;
            }
            default: break;
        }
    }
}



/* 数据处理 */
void User_App_Logic(void)
{
    /* 处理蓝牙数据 */
    if (Ble.flg)
    {
        Ble.flg = false;
        User_App_Sh367601x_Ble_process(Ble.buff, Ble.len);
    }
    /* 处理串口数据 */
    if (Uart.flg)
    {
        Uart.flg = false;
        User_App_Sh367601x_Uart_process(Uart.buff, Uart.len);
    }
    
    /* 队列 */
    if (clock_time_exceed(read_time,  node.time * 1000))
    {
        /* 解锁 */
        sh367601x_task_lock = false;
        node.time = 0;
        if (!queue_empty(&q))
        {
            /* 加锁 */
            sh367601x_task_lock = true;
            queue_pop(&q, &node);
            switch (node.data)
            {
                case QUEUE_CURR_TASK:
                {
                    sh3676010b.comm.method.read_ram(RAM_CURRENT_ADDR_START, RAM_CURRENT_ADDR_LEN);
                    break;
                }
                case QUEUE_RAM_TASK:
                {

                    sh3676010b.comm.method.read_ram(RAM_ADDR_START, RAM_ADDR_LEN);
                    break;
                }
                case QUEUE_ROM_TASK:
                {

                    sh3676010b.comm.method.read_rom(ROM_ADDR_START, ROM_ADDR_LEN);
                    break;
                }
                case QUEUE_WRITE_ENABLE_TASK:
                { 
                    sh3676010b.comm.method.write_command();
                    break;
                }
                /* 通用ROM写入任务处理 - 处理所有QUEUE_WRITE_XX_TASK */
                case QUEUE_WRITE_01_TASK: case QUEUE_WRITE_02_TASK: case QUEUE_WRITE_03_TASK:
                case QUEUE_WRITE_04_TASK: case QUEUE_WRITE_05_TASK: case QUEUE_WRITE_06_TASK:
                case QUEUE_WRITE_07_TASK: case QUEUE_WRITE_08_TASK: case QUEUE_WRITE_09_TASK:
                case QUEUE_WRITE_0A_TASK: case QUEUE_WRITE_0B_TASK: case QUEUE_WRITE_0C_TASK:
                case QUEUE_WRITE_0D_TASK: case QUEUE_WRITE_0E_TASK: case QUEUE_WRITE_0F_TASK:
                case QUEUE_WRITE_10_TASK: case QUEUE_WRITE_11_TASK: case QUEUE_WRITE_12_TASK:
                case QUEUE_WRITE_13_TASK: case QUEUE_WRITE_14_TASK: case QUEUE_WRITE_15_TASK:
                {

                    break;
                }
                case QUEUE_RESET_TASK:
                {
                    sh3676010b.comm.method.reset();
                    
                    /* 检查是否处于批量写ROM流程中 */
                    if (sh3676010b.chip.data.write_rom_in_progress) {
                        /* 当前芯片写入完成，切换到下一个芯片 */
                        sh3676010b.chip.data.current_chip_index++;
                        
                        printf("*********: Chip %d ROM write completed\n", sh3676010b.chip.data.current_chip_index - 1);
                        
                        /* 检查是否还有更多芯片需要写入 */
                        if (sh3676010b.chip.data.current_chip_index < sh3676010b.chip.data.write_rom_target_chip_count) {
                            printf("*********: Switching to next chip %d\n", sh3676010b.chip.data.current_chip_index);
                            
                            /* 切换到下一个芯片并继续写入流程 */
                            sh367601b_switch_to_chip(&sh3676010b, sh3676010b.chip.data.current_chip_index);
                            sh367601b_write_rom_for_chip(&sh3676010b, &q);
                        } else {
                            /* 所有芯片写入完成，结束批量写ROM流程 */
                            sh3676010b.chip.data.write_rom_in_progress = 0;
                            sh3676010b.chip.data.write_rom_target_chip_count = 0;
                            sh3676010b.chip.data.current_chip_index = 0;  /* 重置为第一个芯片 */
                            printf("*********: Batch ROM write completed for all %d chips\n", sh3676010b.chip.data.chip_count);
                        }
                    }
                    
                    break;
                }
                default: break;
            }
        }
        read_time = clock_time();
    }
}

