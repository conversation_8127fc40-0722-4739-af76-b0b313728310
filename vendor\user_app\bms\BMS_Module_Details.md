# BMS模块功能详细说明 v2.0

## 模块概览

BMS系统采用模块化设计，每个模块负责特定的功能领域，通过清晰的接口进行交互。v2.0版本新增了多芯片硬件切换功能和智能任务流程管理。

## 核心BMS模块

### 1. AlarmManager (告警管理器)

**功能**: 监控和管理各种告警状态

**主要数据**:
```c
struct {
    unsigned char ov_alarm;      // 过压告警
    unsigned char uv_alarm;      // 欠压告警
    unsigned char occ_alarm;     // 充电过流告警
    unsigned char ocd_alarm;     // 放电过流告警
    unsigned char otc_alarm;     // 充电过温告警
    unsigned char otd_alarm;     // 放电过温告警
    unsigned char utc_alarm;     // 充电低温告警
    unsigned char utd_alarm;     // 放电低温告警
    unsigned char sc_alarm;      // 短路告警
} data;
```

### 2. VoltageManager (电压管理器)

**功能**: 监控和管理电池电压

**主要数据**:
```c
struct {
    unsigned short cell_voltages[MAX_CELL_COUNT];  // 单体电压数组
    unsigned char battery_count;                   // 电池数量
    unsigned short total_voltage;                  // 总电压
    unsigned short max_voltage;                    // 最高电压
    unsigned short min_voltage;                    // 最低电压
    unsigned char max_voltage_cell;                // 最高电压电池编号
    unsigned char min_voltage_cell;                // 最低电压电池编号
    unsigned short voltage_diff;                   // 压差
} data;
```

### 3. CurrentManager (电流管理器)

**功能**: 监控和管理充放电电流

**主要数据**:
```c
struct {
    int total_current;                    // 总电流(mA)
    int max_charge_current;               // 最大充电电流
    int max_discharge_current;            // 最大放电电流
    CurrentState current_state;           // 电流状态
    unsigned int sampling_time_curr;      // 当前采样时间
    unsigned int sampling_time_last;      // 上次采样时间
    float filtered_current;               // 滤波后电流
} data;
```

### 4. TemperatureManager (温度管理器)

**功能**: 监控和管理温度

**主要数据**:
```c
struct {
    unsigned char external_temp_count;    // 外部温度数量
    unsigned char chip_temp_count;        // 芯片温度数量
    unsigned char mos_temp_count;         // MOS管温度数量
    signed char external_temp[10];        // 外部温度数组
    signed char max_external_temp;        // 最高外部温度
    signed char min_external_temp;        // 最低外部温度
    signed char chip_temp;                // 芯片温度
    signed char mos_temp;                 // MOS管温度
} data;
```

## SH367601B芯片驱动模块

### 1. Config Module (配置管理模块)

**功能**: 管理芯片ROM配置参数

**关键函数**:
```c
// 电压保护
void set_ov(SH367601B_Device* self, unsigned short ov);
void set_uv(SH367601B_Device* self, unsigned short uv);

// 电流保护
void set_occ(SH367601B_Device* self, unsigned char occ);
void set_ocd1(SH367601B_Device* self, unsigned char ocd1);

// 温度保护
void set_otc(SH367601B_Device* self, unsigned char otc);
void set_utc(SH367601B_Device* self, unsigned char utc);
```

### 2. Communication Module (通信驱动模块)

**功能**: 处理与芯片的UART通信

**通信协议**:
```
命令格式: [同步头][命令][地址][长度][CRC]
- 同步头: 0x1C
- 命令: 读ROM(0x03), 读RAM(0x03), 写使能(0x0A), 复位(0x0B)
- 地址: 寄存器地址
- 长度: 数据长度
- CRC: CRC8校验
```

### 3. Parser Module (数据解析模块)

**功能**: 解析芯片返回的原始数据

**数据解析示例**:
```c
// ROM数据解析 (01H寄存器)
rom->enmosr = (data[1] >> 7) & 0x01;  // bit7
rom->chys   = (data[1] >> 6) & 0x01;  // bit6
rom->tc     = (data[1] >> 4) & 0x03;  // bit5-4
rom->cn     = (data[1] >> 0) & 0x0F;  // bit3-0
```

### 4. Converter Module (数据转换模块)

**功能**: 物理量转换和单位换算

**🆕 v2.0改进的电流计算**:
```c
// v2.0简化参数的电流计算
int (*calc_current_from_adc)(unsigned short adc_reg_value, float sampling_resistance_mohm);

// 电压转换 (LSB = 0.25mV)
voltage_mv = adc_value * 0.25;
```

### 5. Tool Module (工具函数模块)

**功能**: 提供各种计算和转换工具

**NTC温度计算**:
```c
// 基于查表法的温度计算
char calc_temp_from_resistance(double resistance_ohm, char temp_offset);

// 混合滤波算法
float hybrid_filter(float new_sample);
```

### 6. Chip Module (芯片管理模块) - **🆕 v2.0增强功能**

**🆕 新增数据成员**:
```c
struct {
    SH367601B_ChipType chip_type[MAX_CHIP_COUNT];
    unsigned char chip_count;
    unsigned char current_chip_index;
    unsigned char write_rom_in_progress;
    unsigned char write_rom_target_chip_count;
    /* 🆕 芯片硬件切换函数指针数组 */
    void (*chip_switch_functions[MAX_CHIP_COUNT])(void);
} data;
```

**🆕 新增方法**:
```c
// 硬件切换管理
void (*set_chip_switch_function)(SH367601B_Device* device, 
                                unsigned char chip_index, 
                                void (*switch_func)(void));

// 任务流程管理
int (*handle_reset_task_chip_switching)(SH367601B_Device* device, Queue* q);
int (*handle_ram_read_chip_switching)(SH367601B_Device* device, Queue* q, bool is_rom_task);
```

**🆕 增强的芯片切换**:
```c
// 切换到指定芯片（自动调用硬件切换函数）
void sh36760_switch_to_chip(SH367601B_Device* device, unsigned char chip_index)
{
    if (device == NULL || chip_index >= device->chip.data.chip_count) {
        return;
    }
    
    device->chip.data.current_chip_index = chip_index;
    
    /* 🆕 调用对应芯片的硬件切换函数 */
    if (device->chip.data.chip_switch_functions[chip_index] != NULL) 
        device->chip.data.chip_switch_functions[chip_index]();
}
```

## 🆕 v2.0新增功能详解

### 1. 硬件切换函数指针数组

**设计理念**: 每个芯片可以有独立的硬件切换实现

**使用场景**:
- GPIO控制多路选择器
- SPI/I2C地址切换
- 模拟开关控制
- 继电器控制

**实现示例**:
```c
// 芯片0的硬件切换函数
void chip0_hardware_switch(void) {
    // 设置GPIO引脚控制多路选择器
    gpio_set_pin(CHIP_SELECT_PIN0, 1);
    gpio_set_pin(CHIP_SELECT_PIN1, 0);
    printf("Hardware switched to chip 0\n");
}

// 注册硬件切换函数
device->chip.method.set_chip_switch_function(device, 0, chip0_hardware_switch);
```

### 2. 任务流程管理

**复位任务芯片切换处理**:
```c
int sh36760_handle_reset_task_chip_switching(SH367601B_Device* device, Queue* q)
{
    // 检查是否处于批量写ROM流程中
    if (!device->chip.data.write_rom_in_progress) {
        return 1; // 不在写ROM流程中，无需处理
    }
    
    // 当前芯片写入完成，检查是否还有下一颗芯片
    unsigned char next_chip_index = device->chip.data.current_chip_index + 1;
    
    if (next_chip_index < device->chip.data.chip_count) {
        // 切换到下一个芯片并继续写入流程
        sh36760_switch_to_chip(device, next_chip_index);
        sh36760_write_rom_for_chip(device, q);
        return 0; // 继续处理下一个芯片
    } else {
        // 所有芯片写入完成
        device->chip.data.write_rom_in_progress = 0;
        return 1; // 所有芯片处理完成
    }
}
```

**RAM读取任务芯片切换处理**:
```c
int sh36760_handle_ram_read_chip_switching(SH367601B_Device* device, Queue* q, bool is_rom_task)
{
    unsigned char next_chip_index = device->chip.data.current_chip_index + 1;
    
    if (next_chip_index < device->chip.data.chip_count) {
        // 切换到下一个芯片并继续任务
        sh36760_switch_to_chip(device, next_chip_index);
        
        if (is_rom_task) {
            queue_push(q, QUEUE_ROM_TASK, QUEUE_ROM_TIME);
        } else {
            queue_push(q, QUEUE_RAM_TASK, QUEUE_RAM_TIME);
        }
        return 0; // 继续处理下一个芯片
    } else {
        // 所有芯片处理完成，重置为第一个芯片
        sh36760_switch_to_chip(device, 0);
        return 1; // 所有芯片处理完成
    }
}
```

### 3. 智能BMS数据更新

**优化策略**: 只在最后一个芯片时更新BMS数据

```c
static void sh367601b_update_bms_from_ram(SH367601B_Device* device)
{
    if (device == NULL) return;
    
    /* 🆕 检查是否为最后一个芯片 */
    unsigned char last_chip_index = device->chip.data.chip_count - 1;
    if (device->chip.data.current_chip_index != last_chip_index) {
        printf("SH367601B: Skip BMS update for chip %d (not last chip)\n", 
               device->chip.data.current_chip_index);
        return;
    }
    
    /* 🆕 收集所有芯片的电压数据 */
    unsigned short all_cell_voltages[MAX_CHIP_COUNT * 16];
    unsigned int total_cell_count = 0;
    
    for (unsigned char chip_idx = 0; chip_idx < device->chip.data.chip_count; chip_idx++) {
        unsigned char actual_cell_count = device->rom[chip_idx].cn;
        unsigned char chip_max_cells = device->chip.method.get_chip_max_cells(device, chip_idx);
        
        if (actual_cell_count > chip_max_cells) actual_cell_count = chip_max_cells;
        
        // 收集当前芯片的电压数据
        for (unsigned char cell_idx = 0; cell_idx < actual_cell_count; cell_idx++) {
            all_cell_voltages[total_cell_count++] = device->ram[chip_idx].vc[cell_idx];
        }
    }
    
    // 更新BMS电压管理器
    VoltageManager *vmgr = &device->bms_system.voltage_mgr;
    vmgr->methods.process_voltage_data(vmgr, all_cell_voltages, total_cell_count, 
                                      device->ram[0].vbat);
    
    printf("SH367601B: BMS data update completed - Total cells: %d from %d chips\n", 
           total_cell_count, device->chip.data.chip_count);
}
```

## 数据流程

### 1. 多芯片数据读取流程 - **🆕 v2.0优化**
```
1. 遍历所有芯片 → 2. 硬件切换 → 3. 读取数据 → 4. 解析数据 → 5. 最后芯片时更新BMS
```

### 2. 批量写ROM流程 - **🆕 v2.0增强**
```
1. 启动批量写ROM → 2. 切换到芯片0 → 3. 写入ROM → 4. 复位任务自动切换 → 5. 重复直到所有芯片完成
```

### 3. 任务流程管理 - **🆕 v2.0新增**
```
1. 队列任务执行 → 2. 任务完成检查 → 3. 芯片切换逻辑 → 4. 继续下一芯片或结束流程
```

## 性能优化

### 1. 🆕 智能数据更新
- 只在最后一个芯片时更新BMS数据
- 避免重复计算，提高效率
- 自动收集所有芯片数据

### 2. 🆕 硬件切换优化
- 函数指针避免条件判断开销
- 每个芯片独立的切换逻辑
- 减少硬件切换时间

### 3. 🆕 任务流程优化
- 自动化芯片切换流程
- 减少手动干预
- 提高批量操作效率

## 错误处理

### 1. 🆕 硬件切换错误
- 函数指针空检查
- 芯片索引有效性验证
- 切换失败恢复机制

### 2. 🆕 任务流程错误
- 参数有效性检查
- 流程状态验证
- 异常情况处理

### 3. 数据一致性
- 多芯片数据同步
- 状态一致性检查
- 错误数据过滤

---

**版本**: 2.0 | **日期**: 2025 | **模型**: Claude Sonnet 4
