#include "sh367601xb_chip.h"
#include "../../user_app_main.h"  /* 引入队列定义 */


/**
 * @brief 切换到指定芯片（设置当前芯片索引并执行IO切换）
 * @param device 设备实例指针
 * @param chip_index 目标芯片索引 (0-MAX_CHIP_COUNT-1)
 * @return 0=成功，-1=失败
 */
int sh367601b_switch_to_chip(SH367601B_Device* device, unsigned char chip_index)
{
    /* 设置当前芯片索引到结构体 */
    device->chip.data.current_chip_index = chip_index;
    printf("SH367601B: Current chip index set to %d\n", chip_index);
    
    switch (device->chip.data.current_chip_index)
    {
        case 0:
            break;
        case 1:
            break;
        case 2:
            break;
        case 3:
            break;
        case 4:
            break;
        case 5:
            break;
        case 6:
            break;
        case 7:
            break;
        default: break;
    }
    
    printf("SH367601B: Successfully switched to chip %d\n", chip_index);
    return 0;
}

/**
 * @brief 写入ROM数据（基于队列机制）
 * @param device 设备实例指针
 * @param q 队列指针（来自主程序的全局队列）
 * @return 0=成功，-1=失败
 */
int sh367601b_write_rom_for_chip(SH367601B_Device* device, Queue* q)
{
    /* 填充字节数组 */
    /* 00H */
    device->write_buff[0] = device->write_rom.id;
    /* 01H: enmosr, chys, tc, cn */
    device->write_buff[1] = (device->write_rom.enmosr << 7) | (device->write_rom.chys << 6) | (device->write_rom.tc << 4) | device->write_rom.cn;
    /* 02H: bals, chs, ocra, eovr, euvr, eow, eot3, enmos */
    device->write_buff[2] = (device->write_rom.bals << 7) | (device->write_rom.chs << 6) | (device->write_rom.ocra << 5) | 
                  (device->write_rom.eovr << 4) | (device->write_rom.euvr << 3) | (device->write_rom.eow << 2) | 
                  (device->write_rom.eot3 << 1) | device->write_rom.enmos;
    /* 03H,04H,05H: ovt, ov, ovr */
    device->write_buff[3] = (device->write_rom.ovt << 6) | ((device->write_rom.ov >> 4) & 0x3F);
    device->write_buff[4] = ((device->write_rom.ov & 0x0F) << 4) | ((device->write_rom.ovr >> 8) & 0x01);
    device->write_buff[5] = device->write_rom.ovr & 0xFF;
    /* 06H: uvr */
    device->write_buff[6] = device->write_rom.uvr;
    /* 07H,08H: lov, balt, uvt, uv */
    device->write_buff[7] = (device->write_rom.lov << 5) | (device->write_rom.balt << 4) | (device->write_rom.uvt << 1) | ((device->write_rom.uv >> 8) & 0x01);
    device->write_buff[8] = device->write_rom.uv & 0xFF;
    /* 09H: balv */
    device->write_buff[9] = device->write_rom.balv;
    /* 0AH: bald, ocd1v, ocd1t */
    device->write_buff[10] = (device->write_rom.bald << 6) | (device->write_rom.ocd1v << 2) | device->write_rom.ocd1t;
    /* 0BH: sct, ocd2v, ocd2t */
    device->write_buff[11] = (device->write_rom.sct << 5) | (device->write_rom.ocd2v << 2) | device->write_rom.ocd2t;
    /* 0CH: occv, occt */
    device->write_buff[12] = (device->write_rom.occv << 2) | device->write_rom.occt;
    /* 0DH~14H: otc, otcr, otd, otdr, utc, utcr, utd, utdr */
    device->write_buff[13] = device->write_rom.otc;
    device->write_buff[14] = device->write_rom.otcr;
    device->write_buff[15] = device->write_rom.otd;
    device->write_buff[16] = device->write_rom.otdr;
    device->write_buff[17] = device->write_rom.utc;
    device->write_buff[18] = device->write_rom.utcr;
    device->write_buff[19] = device->write_rom.utd;
    device->write_buff[20] = device->write_rom.utdr;
    /* 将写入任务按顺序加入队列 */
    /* 1. 首先发送写使能命令 */
    queue_push(q, QUEUE_WRITE_ENABLE_TASK, QUEUE_WRITE_ENABLE_TIME);
    /* 2. 按照write_flags标志，将需要写入的地址加入队列 */
    for (unsigned char addr = 0; addr < ROM_ADDR_LEN; addr++) {
        if (device->write_flags[addr]) 
            queue_push(q, QUEUE_WRITE_01_TASK + addr, QUEUE_WRITE_TIME);
    }
    /* 3. 最后发送复位命令完成写入 */
    queue_push(q, QUEUE_RESET_TASK, QUEUE_RESET_TIME);
    return 0;
}

/**
 * @brief 设置芯片数量
 * @param device 设备实例指针
 * @param chip_count 芯片数量 (1-MAX_CHIP_COUNT)
 * @return 0=成功，-1=失败
 */
int sh367601b_set_chip_count(SH367601B_Device* device, unsigned char chip_count)
{

    /* 设置芯片数量 */
    device->chip.data.chip_count = chip_count;
    
    /* 如果当前选中的芯片索引超出新的芯片数量范围，则重置为0 */
    if (device->chip.data.current_chip_index >= chip_count) {
        device->chip.data.current_chip_index = 0;
        printf("SH367601B: Current chip index reset to 0 due to chip count change\n");
    }
    
    printf("SH367601B: Chip count set to %d\n", chip_count);
    return 0;
}

/**
 * @brief 启动批量写ROM流程（自动遍历所有芯片）
 * @param device 设备实例指针
 * @param q 队列指针（来自主程序的全局队列）
 * @return 0=成功，-1=失败
 */
int sh367601b_start_batch_write_rom(SH367601B_Device* device, Queue* q)
{
    /* 设置批量写ROM流程状态 */
    device->chip.data.write_rom_in_progress = 1;
    device->chip.data.write_rom_target_chip_count = device->chip.data.chip_count;
    device->chip.data.current_chip_index = 0;  /* 从第0个芯片开始 */
    
    printf("SH367601B: Starting batch ROM write for %d chips\n", device->chip.data.chip_count);
    
    /* 切换到第一个芯片并开始写入流程 */
    sh367601b_switch_to_chip(device, 0);
    sh367601b_write_rom_for_chip(device, q);
    
    return 0;
}

/* ==========================================SH36760芯片模块函数指针接口实现========================================== */

/**
 * @brief SH36760芯片接口实例（静态全局变量）
 * @note 包含所有芯片操作函数的指针，实现统一的接口调用
 */
static SH36760_ChipInterface sh36760_chip_interface = {
    .switch_to_chip = sh367601b_switch_to_chip,
    .write_rom_for_chip = sh367601b_write_rom_for_chip,
    .set_chip_count = sh367601b_set_chip_count,
    .start_batch_write_rom = sh367601b_start_batch_write_rom
};

/**
 * @brief 获取SH36760芯片接口实例
 * @return SH36760芯片接口结构体指针
 * @note 返回包含所有芯片操作函数指针的接口结构体，可用于统一调用不同芯片的实现
 */
SH36760_ChipInterface* sh36760_get_chip_interface(void)
{
    return &sh36760_chip_interface;
}
