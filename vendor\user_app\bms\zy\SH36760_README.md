# SH36760芯片模块 - 基于函数指针的芯片操作接口

## 概述

本模块将`sh367601xb_chip.c`文件中的方法通过函数指针的方式集成到SH36760芯片模块中，提供了统一的芯片操作接口。

## 文件结构

```
vendor/user_app/bms/zy/
├── sh36760_chip.h          # SH36760芯片模块头文件
├── sh36760_chip.c          # SH36760芯片模块实现文件
├── sh36760_example.h       # 使用示例头文件
├── sh36760_example.c       # 使用示例实现文件
├── sh367601xb_chip.h       # 原始芯片模块头文件（已扩展）
├── sh367601xb_chip.c       # 原始芯片模块实现文件（已扩展）
└── SH36760_README.md       # 本说明文件
```

## 主要功能

### 1. 核心芯片操作接口

通过`SH36760_ChipInterface`结构体提供的函数指针：

- `switch_to_chip()` - 切换到指定芯片
- `write_rom_for_chip()` - 写入ROM数据（基于队列机制）
- `set_chip_count()` - 设置芯片数量
- `start_batch_write_rom()` - 启动批量写ROM流程

### 2. 扩展功能

- `get_current_chip_index()` - 获取当前芯片索引
- `get_chip_count()` - 获取芯片数量
- `is_write_rom_in_progress()` - 检查写ROM流程状态

### 3. 模块管理器

通过`SH36760_ChipManager`提供：

- 模块初始化和重置
- 模块信息查询
- 统一的管理接口

## 使用方法

### 基本使用

```c
#include "sh36760_chip.h"

// 1. 初始化模块
sh36760_chip_module_init();

// 2. 获取芯片操作接口
SH36760_ChipInterface* interface = sh36760_get_chip_interface();

// 3. 使用函数指针调用芯片操作
SH367601B_Device device;
Queue queue;

// 设置芯片数量
interface->set_chip_count(&device, 4);

// 切换到芯片1
interface->switch_to_chip(&device, 1);

// 写入ROM数据
interface->write_rom_for_chip(&device, &queue);

// 启动批量写ROM流程
interface->start_batch_write_rom(&device, &queue);
```

### 使用管理器

```c
// 获取芯片管理器
SH36760_ChipManager* manager = sh36760_get_chip_manager();

// 初始化模块
manager->methods.init();

// 获取模块信息
manager->methods.get_info();

// 重置模块
manager->methods.reset();
```

### 状态查询

```c
// 获取当前芯片信息
unsigned char chip_count = interface->get_chip_count(&device);
unsigned char current_index = interface->get_current_chip_index(&device);
unsigned char is_writing = interface->is_write_rom_in_progress(&device);

printf("Chip count: %d, Current: %d, Writing: %s\n", 
       chip_count, current_index, is_writing ? "Yes" : "No");
```

## 运行示例

```c
#include "sh36760_example.h"

// 运行所有示例
sh36760_run_all_examples(&device, &queue);

// 或者运行单个示例
sh36760_basic_usage_example(&device, &queue);
sh36760_manager_usage_example();
sh36760_batch_write_example(&device, &queue);
```

## 实现原理

### 1. 函数指针封装

将`sh367601xb_chip.c`中的核心函数通过函数指针封装：

```c
static SH36760_ChipInterface sh36760_chip_interface = {
    .switch_to_chip = sh367601b_switch_to_chip,
    .write_rom_for_chip = sh367601b_write_rom_for_chip,
    .set_chip_count = sh367601b_set_chip_count,
    .start_batch_write_rom = sh367601b_start_batch_write_rom,
    // 扩展功能
    .get_current_chip_index = sh36760_get_current_chip_index,
    .get_chip_count = sh36760_get_chip_count,
    .is_write_rom_in_progress = sh36760_is_write_rom_in_progress
};
```

### 2. 统一接口访问

通过`sh36760_get_chip_interface()`函数获取接口实例，实现统一的调用方式。

### 3. 模块管理

通过`SH36760_ChipManager`提供完整的模块管理功能，包括初始化、信息查询和重置。

## 优势特点

1. **复用性强** - 直接复用`sh367601xb_chip.c`中的成熟功能
2. **接口统一** - 通过函数指针提供统一的调用接口
3. **扩展性好** - 易于添加新的芯片操作功能
4. **管理完善** - 提供完整的模块管理和状态查询
5. **示例丰富** - 包含详细的使用示例和文档

## 技术细节

### 原始功能集成

从`sh367601xb_chip.c`集成的核心功能：

- **芯片切换** - `sh367601b_switch_to_chip()`
- **ROM写入** - `sh367601b_write_rom_for_chip()`
- **芯片数量管理** - `sh367601b_set_chip_count()`
- **批量写入** - `sh367601b_start_batch_write_rom()`

### 扩展功能实现

新增的扩展功能：

- **状态查询** - 获取当前芯片索引、芯片数量、写入状态
- **模块管理** - 初始化、信息显示、重置功能
- **使用示例** - 完整的示例代码和使用指南

## 注意事项

1. 使用前需要先初始化模块：`sh36760_chip_module_init()`
2. 确保设备实例和队列指针有效
3. 函数指针调用前检查接口是否为NULL
4. 批量操作时注意队列的状态管理

## 版本信息

- **版本**: 1.0
- **日期**: 2025
- **作者**: 嵌入式开发专家
- **模型**: Claude Sonnet 4

---

通过这个模块，您可以方便地使用函数指针方式调用原本在`sh367601xb_chip.c`中实现的所有芯片操作功能，同时获得更好的代码组织和扩展性。
