#include "bms_data_tool.h"

/* ==========================================SOC========================================== */
/**
 * @brief  计算SOC
 * @param  soc         当前SOC（%）
 * @param  capacity_mAh 电池容量（mAh）
 * @param  current_mA  当前电流（mA）
 * @param  time_ms     时间间隔（ms）
 * @return 更新后的SOC（%）
 */
float soc_compute(float soc, float capacity_mAh, float current_mA, float time_ms, unsigned char direction)
{
    /* 1. 计算SOC变化量 */
    float delta_soc = (current_mA * time_ms) / (capacity_mAh * 3600.0f * 1000.0f) * 100.0f;
    
    /* 2. 更新SOC */
    if (1 == direction) soc -= delta_soc;
    else soc += delta_soc;
    
    /* 3. SOC边界保护 */
    if (soc > 100.0f) soc = 100.0f;
    else if (soc < 0.0f) soc = 0.0f;
    
    return soc;
}

