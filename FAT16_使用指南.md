## 📊 系统规格

| 项目 | 规格 | 说明 |
|------|------|------|
| 存储空间 | 192KB (0x40000-0x70000) | Flash地址范围 |
| 最大文件数 | 16个 | 根目录项限制 |
| 单文件最大 | 64KB | 优化后支持更大文件 |
| 文件名格式 | 8.3格式 | DOS兼容格式 |
| 簇大小 | 1KB | 2个512字节扇区 |
| 日志文件最大 | 8KB | 支持滚动日志 |
| 内存使用 | Flash优化 | 针对Flash存储优化 | 

### 💾 存储限制
- 最大文件数量：16个文件
- 单文件最大：64KB（Flash存储优化）
- 总存储空间：192KB（0x40000-0x70000）
- 簇大小：1KB（1024字节）
- 日志文件：8KB缓冲区，支持滚动 

### 🧠 内存使用
- **Flash存储优化**：充分利用Flash存储空间特性
- **栈使用适中**：最大约8KB（日志缓冲区）
- **无动态分配**：避免内存碎片问题
- **缓存优化**：256字节FAT表缓存
- **簇链处理**：支持64个簇（64KB文件） 

### 🚫 避免事项

1. **不要**超过16个文件限制
2. **不要**创建过大的单个文件（>64KB）
3. **不要**使用非法文件名字符
4. **不要**在中断中执行文件操作
5. **不要**频繁进行大量写入操作
6. **注意**日志文件会在8KB时自动滚动 

### 🔄 系统管理

| 函数名 | 功能 | 返回值 |
|--------|------|--------|
| `FAT16_Init()` | 智能初始化文件系统 | 0=成功, -1=失败 |
| `FAT16_ForceFormat()` | 强制格式化（清除数据） | 0=成功, -1=失败 |
| `FAT16_FlashHealthCheck()` | Flash 健康检查 | 0=健康, -1=有问题 |
| `FAT16_GetUsagePercent()` | 获取存储使用率 | 0-100百分比, -1=错误 |
| `FAT16_ClearAll()` | 清空所有文件 | 0=成功, -1=失败 | 

### 1. 智能初始化文件系统

```c
#include "user_app_flash.h"

int main()
{
    // 智能初始化 FAT16 文件系统
    // - 如果发现现有文件系统，会自动恢复（保护数据）
    // - 如果没有文件系统，才会格式化
    if (FAT16_Init() != 0) {
        printf("文件系统初始化失败！\n");
        return -1;
    }
    printf("文件系统初始化成功！\n");
    
    return 0;
}
```

### 📋 **智能初始化特性**

FAT16文件系统采用智能初始化机制，**自动保护您的数据**：

✅ **数据保护**：上电后自动检测现有文件系统  
✅ **无损恢复**：发现有效文件系统时，保留所有文件  
✅ **智能格式化**：仅在必要时（无文件系统或损坏）才格式化  
✅ **状态重建**：自动扫描FAT表，重建文件系统状态信息  

```c
// 系统启动时的初始化流程
FAT16_Init();  // 智能初始化，保护现有数据

// 如果确实需要清除所有数据
FAT16_ForceFormat();  // 强制格式化，清除所有文件
```

### 2. 检查系统状态 