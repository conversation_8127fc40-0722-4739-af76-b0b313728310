# FAT16 文件系统使用指南

## 📋 概述

本文档详细介绍了基于 Flash 存储的 FAT16 文件系统的使用方法。该文件系统采用**无动态内存分配**设计，专为嵌入式系统优化，提供了简单易用的文件增删改查接口，特别适用于 BMS 等资源受限的嵌入式系统。

## 🔧 系统特性

- **Flash 存储**: 基于内部 Flash 存储器实现
- **FAT16 兼容**: 标准 FAT16 文件系统格式  
- **无动态内存**: 完全使用栈上分配，无 malloc/free
- **自动管理**: 自动初始化、健康检查、内存管理
- **便捷接口**: 简化的 API 设计，易于使用
- **安全可靠**: 内置 Flash 健康检查和错误处理
- **嵌入式友好**: 适合资源受限环境，栈使用量可预测

## 📊 系统规格

| 项目 | 规格 | 说明 |
|------|------|------|
| 存储空间 | 192KB (0x40000-0x70000) | Flash地址范围 |
| 最大文件数 | 16个 | 根目录项限制 |
| 单文件最大 | 16KB | 受簇链数组限制 |
| 文件名格式 | 8.3格式 | DOS兼容格式 |
| 簇大小 | 1KB | 2个512字节扇区 |
| 内存使用 | 静态分配 | 无动态内存分配 |

## 📚 API 接口一览

### 🔄 系统管理

| 函数名 | 功能 | 返回值 |
|--------|------|--------|
| `FAT16_Init()` | 初始化文件系统 | 0=成功, -1=失败 |
| `FAT16_FlashHealthCheck()` | Flash 健康检查 | 0=健康, -1=有问题 |
| `FAT16_GetUsagePercent()` | 获取存储使用率 | 0-100百分比, -1=错误 |
| `FAT16_ClearAll()` | 清空所有文件 | 0=成功, -1=失败 |

### 📁 文件操作

| 函数名 | 功能 | 返回值 |
|--------|------|--------|
| `FAT16_WriteFile()` | 写入文件（创建/覆盖） | 0=成功, -1=失败 |
| `FAT16_ReadFileData()` | 读取文件内容 | 读取字节数, -1=失败 |
| `FAT16_FileExists()` | 检查文件是否存在 | 1=存在, 0=不存在, -1=错误 |
| `FAT16_GetFileSize()` | 获取文件大小 | 文件大小(字节), -1=失败 |
| `FAT16_ListFiles()` | 列出所有文件 | 文件数量, -1=错误 |

### 🎯 专用接口

| 函数名 | 功能 | 返回值 |
|--------|------|--------|
| `FAT16_SaveConfig()` | 保存配置文件 | 0=成功, -1=失败 |
| `FAT16_LoadConfig()` | 读取配置文件 | 读取字节数, -1=失败 |
| `FAT16_AppendLog()` | 追加日志内容 | 0=成功, -1=失败 |

## 🚀 快速开始

### 1. 初始化文件系统

```c
#include "user_app_flash.h"

int main()
{
    // 初始化 FAT16 文件系统（包含 Flash 健康检查）
    if (FAT16_Init() != 0) {
        printf("文件系统初始化失败！\n");
        return -1;
    }
    printf("文件系统初始化成功！\n");
    
    return 0;
}
```

### 2. 检查系统状态

```c
void check_system_status()
{
    // 检查 Flash 健康状态
    if (FAT16_FlashHealthCheck() == 0) {
        printf("Flash 设备健康✅\n");
    } else {
        printf("Flash 设备异常❌\n");
    }
    
    // 获取存储使用率
    int usage = FAT16_GetUsagePercent();
    if (usage >= 0) {
        printf("存储使用率: %d%%\n", usage);
    }
}
```

## 📝 文件增删改查操作

### ➕ 增 - 创建/写入文件

#### 基本文件写入

```c
void create_file_example()
{
    // 准备数据
    unsigned char data[] = "Hello, FAT16 File System!";
    
    // 写入文件（如果文件存在则覆盖）
    int result = FAT16_WriteFile("hello.txt", data, strlen((char*)data));
    
    if (result == 0) {
        printf("文件创建成功！\n");
    } else {
        printf("文件创建失败！\n");
    }
}
```

#### 写入BMS传感器数据

```c
void write_bms_sensor_data()
{
    // BMS传感器数据结构
    typedef struct {
        uint16_t cell_voltages[16];  // 单体电压 (mV)
        uint16_t pack_voltage;       // 总电压 (mV)
        int16_t  current;            // 电流 (mA)
        int8_t   temperatures[4];    // 温度 (°C)
        uint8_t  soc;               // SOC (%)
        uint32_t timestamp;         // 时间戳
    } bms_data_t;
    
    bms_data_t bms_data = {
        .cell_voltages = {3300, 3310, 3305, 3298, 3302, 3307, 3295, 3301,
                         3299, 3304, 3306, 3297, 3303, 3308, 3296, 3300},
        .pack_voltage = 52800,  // 16 * 3.3V
        .current = 2500,        // 2.5A放电
        .temperatures = {25, 26, 24, 25},
        .soc = 75,              // 75%
        .timestamp = 1234567890
    };
    
    int result = FAT16_WriteFile("bms_data.bin", 
                                (unsigned char*)&bms_data, 
                                sizeof(bms_data_t));
    
    if (result == 0) {
        printf("BMS数据保存成功！\n");
    }
}
```

#### 批量创建数据文件

```c
void create_daily_data_files()
{
    char filename[20];
    unsigned char data[512];
    
    for (int day = 1; day <= 7; day++) {
        // 生成文件名：day1.dat, day2.dat...
        sprintf(filename, "day%d.dat", day);
        
        // 生成模拟数据
        for (int i = 0; i < 512; i++) {
            data[i] = (day * 10 + i) & 0xFF;
        }
        
        // 写入文件
        if (FAT16_WriteFile(filename, data, 512) == 0) {
            printf("日数据文件 %s 创建成功\n", filename);
        }
    }
}
```

### 🔍 查 - 文件查询操作

#### 检查重要文件是否存在

```c
void check_important_files()
{
    const char* important_files[] = {
        "config.dat",    // 配置文件
        "system.log",    // 系统日志
        "bms_data.bin",  // BMS数据
        "calib.dat"      // 校准数据
    };
    
    int file_count = sizeof(important_files) / sizeof(important_files[0]);
    
    printf("检查重要文件状态:\n");
    for (int i = 0; i < file_count; i++) {
        int exists = FAT16_FileExists(important_files[i]);
        
        if (exists == 1) {
            int size = FAT16_GetFileSize(important_files[i]);
            printf("✅ %s (大小: %d 字节)\n", important_files[i], size);
        } else if (exists == 0) {
            printf("❌ %s 不存在\n", important_files[i]);
        } else {
            printf("⚠️  检查 %s 时出错\n", important_files[i]);
        }
    }
}
```

#### 获取文件系统统计信息

```c
void get_filesystem_stats()
{
    char file_list[16][13];
    int file_count = FAT16_ListFiles(file_list, 16);
    
    if (file_count > 0) {
        printf("文件系统统计:\n");
        printf("%-12s %8s\n", "文件名", "大小(字节)");
        printf("========================\n");
        
        int total_files = 0;
        int total_size = 0;
        
        for (int i = 0; i < file_count; i++) {
            int size = FAT16_GetFileSize(file_list[i]);
            if (size >= 0) {
                printf("%-12s %8d\n", file_list[i], size);
                total_files++;
                total_size += size;
            }
        }
        
        printf("========================\n");
        printf("文件总数: %d/%d\n", total_files, 16);
        printf("总大小: %d 字节\n", total_size);
        printf("使用率: %d%%\n", FAT16_GetUsagePercent());
    } else {
        printf("文件系统为空或查询失败\n");
    }
}
```

### 📖 读 - 文件读取操作

#### 读取BMS配置数据

```c
void read_bms_config()
{
    typedef struct {
        uint16_t cell_ovp;          // 单体过压保护 (mV)
        uint16_t cell_uvp;          // 单体欠压保护 (mV)
        uint16_t pack_ovp;          // 总压过压保护 (mV)
        uint16_t pack_uvp;          // 总压欠压保护 (mV)
        uint16_t charge_ocp;        // 充电过流保护 (mA)
        uint16_t discharge_ocp;     // 放电过流保护 (mA)
        int8_t   charge_otp;        // 充电过温保护 (°C)
        int8_t   discharge_otp;     // 放电过温保护 (°C)
        uint8_t  cell_count;        // 电池串数
        uint8_t  reserved[3];       // 保留字节
    } bms_config_t;
    
    bms_config_t config;
    
    int bytes_read = FAT16_ReadFileData("bms_cfg.bin", 
                                       (unsigned char*)&config, 
                                       sizeof(bms_config_t));
    
    if (bytes_read == sizeof(bms_config_t)) {
        printf("BMS配置读取成功:\n");
        printf("  单体过压: %d mV\n", config.cell_ovp);
        printf("  单体欠压: %d mV\n", config.cell_uvp);
        printf("  充电过流: %d mA\n", config.charge_ocp);
        printf("  放电过流: %d mA\n", config.discharge_ocp);
        printf("  充电过温: %d °C\n", config.charge_otp);
        printf("  放电过温: %d °C\n", config.discharge_otp);
        printf("  电池串数: %d\n", config.cell_count);
    } else {
        printf("BMS配置读取失败！\n");
    }
}
```

#### 读取文本日志文件

```c
void read_system_log()
{
    unsigned char log_buffer[1024];  // 1KB缓冲区
    
    int log_size = FAT16_ReadFileData("system.log", log_buffer, sizeof(log_buffer) - 1);
    
    if (log_size > 0) {
        log_buffer[log_size] = '\0';  // 确保字符串结束
        printf("系统日志内容:\n");
        printf("================\n");
        printf("%s", (char*)log_buffer);
        printf("================\n");
        printf("日志大小: %d 字节\n", log_size);
    } else if (log_size == 0) {
        printf("日志文件为空\n");
    } else {
        printf("读取日志文件失败\n");
    }
}
```

### ❌ 删 - 文件删除操作

#### 清理临时文件

```c
void cleanup_temp_files()
{
    char file_list[16][13];
    int file_count = FAT16_ListFiles(file_list, 16);
    
    if (file_count <= 0) {
        printf("没有文件可检查\n");
        return;
    }
    
    printf("检查临时文件...\n");
    
    // 标记需要删除的文件（因为FAT16_DeleteFile是内部函数）
    // 这里演示逻辑，实际需要通过重建文件系统来删除
    int temp_count = 0;
    for (int i = 0; i < file_count; i++) {
        // 检查是否为临时文件（以temp开头或.tmp结尾）
        if (strncmp(file_list[i], "temp", 4) == 0 || 
            strstr(file_list[i], ".tmp") != NULL) {
            printf("发现临时文件: %s\n", file_list[i]);
            temp_count++;
        }
    }
    
    if (temp_count > 0) {
        printf("发现 %d 个临时文件，建议清理\n", temp_count);
    } else {
        printf("没有找到临时文件\n");
    }
}
```

#### 完全清空文件系统

```c
void format_filesystem()
{
    printf("准备格式化文件系统...\n");
    
    // 先显示当前文件
    char file_list[16][13];
    int file_count = FAT16_ListFiles(file_list, 16);
    
    if (file_count > 0) {
        printf("当前有 %d 个文件:\n", file_count);
        for (int i = 0; i < file_count; i++) {
            printf("  - %s\n", file_list[i]);
        }
        
        // 清空所有文件
        if (FAT16_ClearAll() == 0) {
            printf("✅ 文件系统已清空\n");
        } else {
            printf("❌ 清空失败\n");
        }
    } else {
        printf("文件系统已经为空\n");
    }
}
```

## 🎯 专用接口使用

### BMS配置管理

```c
typedef struct {
    uint16_t version;               // 配置版本
    uint16_t cell_count;            // 电池串数
    uint16_t cell_ovp_mv;          // 单体过压 (mV)
    uint16_t cell_uvp_mv;          // 单体欠压 (mV)
    uint16_t pack_ovp_mv;          // 总压过压 (mV)
    uint16_t pack_uvp_mv;          // 总压欠压 (mV)
    uint16_t charge_ocp_ma;        // 充电过流 (mA)
    uint16_t discharge_ocp_ma;     // 放电过流 (mA)
    int8_t   charge_otp_c;         // 充电过温 (°C)
    int8_t   discharge_otp_c;      // 放电过温 (°C)
    int8_t   charge_utp_c;         // 充电低温 (°C)
    int8_t   discharge_utp_c;      // 放电低温 (°C)
    uint16_t balance_start_mv;     // 均衡开启电压差 (mV)
    uint16_t balance_stop_mv;      // 均衡停止电压差 (mV)
    uint32_t serial_number;        // 序列号
    uint32_t checksum;             // 校验和
} bms_config_t;

void bms_config_management()
{
    bms_config_t config = {
        .version = 1,
        .cell_count = 16,
        .cell_ovp_mv = 4250,        // 4.25V
        .cell_uvp_mv = 2800,        // 2.8V
        .pack_ovp_mv = 68000,       // 68V
        .pack_uvp_mv = 44800,       // 44.8V
        .charge_ocp_ma = 10000,     // 10A
        .discharge_ocp_ma = 20000,  // 20A
        .charge_otp_c = 45,         // 45°C
        .discharge_otp_c = 60,      // 60°C
        .charge_utp_c = 0,          // 0°C
        .discharge_utp_c = -10,     // -10°C
        .balance_start_mv = 50,     // 50mV
        .balance_stop_mv = 20,      // 20mV
        .serial_number = 20240001,
        .checksum = 0
    };
    
    // 计算校验和
    config.checksum = 0;
    unsigned char* ptr = (unsigned char*)&config;
    for (int i = 0; i < sizeof(bms_config_t) - sizeof(uint32_t); i++) {
        config.checksum += ptr[i];
    }
    
    // 保存配置
    if (FAT16_SaveConfig((unsigned char*)&config, sizeof(bms_config_t)) == 0) {
        printf("✅ BMS配置保存成功\n");
    } else {
        printf("❌ BMS配置保存失败\n");
        return;
    }
    
    // 读取并验证配置
    bms_config_t loaded_config;
    int bytes_read = FAT16_LoadConfig((unsigned char*)&loaded_config, sizeof(bms_config_t));
    
    if (bytes_read == sizeof(bms_config_t)) {
        // 验证校验和
        uint32_t calc_checksum = 0;
        unsigned char* check_ptr = (unsigned char*)&loaded_config;
        for (int i = 0; i < sizeof(bms_config_t) - sizeof(uint32_t); i++) {
            calc_checksum += check_ptr[i];
        }
        
        if (calc_checksum == loaded_config.checksum) {
            printf("✅ BMS配置读取成功，校验通过\n");
            printf("  版本: %d\n", loaded_config.version);
            printf("  电池串数: %d\n", loaded_config.cell_count);
            printf("  单体过压: %d mV\n", loaded_config.cell_ovp_mv);
            printf("  单体欠压: %d mV\n", loaded_config.cell_uvp_mv);
            printf("  序列号: %u\n", loaded_config.serial_number);
        } else {
            printf("❌ 配置校验失败\n");
        }
    } else {
        printf("❌ BMS配置读取失败\n");
    }
}
```

### 系统日志管理

```c
void bms_logging_system()
{
    // 系统启动日志
    const char* startup_msg = "[SYSTEM] BMS System Started v1.0\n";
    FAT16_AppendLog((unsigned char*)startup_msg, strlen(startup_msg));
    
    // 配置加载日志
    const char* config_msg = "[CONFIG] Configuration loaded successfully\n";
    FAT16_AppendLog((unsigned char*)config_msg, strlen(config_msg));
    
    // 实时状态日志
    char status_log[128];
    sprintf(status_log, "[STATUS] Cell1:3.30V Cell2:3.31V Pack:52.8V Cur:2.5A Temp:25C SOC:75%%\n");
    FAT16_AppendLog((unsigned char*)status_log, strlen(status_log));
    
    // 告警日志
    const char* alarm_msg = "[ALARM] Cell voltage difference >50mV, balancing started\n";
    FAT16_AppendLog((unsigned char*)alarm_msg, strlen(alarm_msg));
    
    printf("日志记录完成\n");
    
    // 读取并显示日志
    unsigned char log_buffer[1024];
    int log_size = FAT16_ReadFileData("system.log", log_buffer, sizeof(log_buffer) - 1);
    
    if (log_size > 0) {
        log_buffer[log_size] = '\0';
        printf("\n当前系统日志:\n");
        printf("==================\n");
        printf("%s", (char*)log_buffer);
        printf("==================\n");
    }
}
```

## 📊 系统监控和维护

### 存储健康监控

```c
void storage_health_monitoring()
{
    printf("=== 存储健康监控 ===\n");
    
    // 1. Flash健康检查
    printf("1. Flash设备健康检查\n");
    if (FAT16_FlashHealthCheck() == 0) {
        printf("✅ Flash设备健康正常\n");
    } else {
        printf("❌ Flash设备异常！\n");
        return;
    }
    
    // 2. 存储使用情况
    printf("\n2. 存储使用情况\n");
    int usage_percent = FAT16_GetUsagePercent();
    if (usage_percent >= 0) {
        printf("存储使用率: %d%%\n", usage_percent);
        
        if (usage_percent > 90) {
            printf("🚨 警告: 存储空间严重不足！\n");
        } else if (usage_percent > 75) {
            printf("⚠️  注意: 存储空间使用率较高\n");
        } else {
            printf("✅ 存储空间充足\n");
        }
    }
    
    // 3. 文件统计
    printf("\n3. 文件统计信息\n");
    char file_list[16][13];
    int file_count = FAT16_ListFiles(file_list, 16);
    
    if (file_count > 0) {
        int total_size = 0;
        int config_files = 0;
        int log_files = 0;
        int data_files = 0;
        
        for (int i = 0; i < file_count; i++) {
            int size = FAT16_GetFileSize(file_list[i]);
            if (size > 0) {
                total_size += size;
                
                // 分类统计
                if (strstr(file_list[i], "config") || strstr(file_list[i], ".cfg")) {
                    config_files++;
                } else if (strstr(file_list[i], "log") || strstr(file_list[i], ".log")) {
                    log_files++;
                } else {
                    data_files++;
                }
            }
        }
        
        printf("文件总数: %d/%d\n", file_count, 16);
        printf("总占用: %d 字节\n", total_size);
        printf("配置文件: %d 个\n", config_files);
        printf("日志文件: %d 个\n", log_files);
        printf("数据文件: %d 个\n", data_files);
    } else {
        printf("文件系统为空\n");
    }
    
    printf("\n=== 监控完成 ===\n");
}
```

### 文件系统维护

```c
void filesystem_maintenance()
{
    printf("=== 文件系统维护 ===\n");
    
    // 1. 健康检查
    printf("1. 执行健康检查...\n");
    if (FAT16_FlashHealthCheck() != 0) {
        printf("❌ Flash健康检查失败，尝试重新初始化\n");
        if (FAT16_Init() == 0) {
            printf("✅ 重新初始化成功\n");
        } else {
            printf("❌ 重新初始化失败，请检查硬件\n");
            return;
        }
    } else {
        printf("✅ Flash健康状态正常\n");
    }
    
    // 2. 检查关键文件
    printf("\n2. 检查关键文件...\n");
    const char* critical_files[] = {"config.dat"};
    
    for (int i = 0; i < 1; i++) {
        if (FAT16_FileExists(critical_files[i]) != 1) {
            printf("⚠️  关键文件 %s 缺失\n", critical_files[i]);
        } else {
            printf("✅ 关键文件 %s 存在\n", critical_files[i]);
        }
    }
    
    // 3. 存储空间检查
    printf("\n3. 存储空间检查...\n");
    int usage = FAT16_GetUsagePercent();
    if (usage > 85) {
        printf("⚠️  存储空间不足，建议清理\n");
        
        // 可以在这里添加自动清理逻辑
        // 比如删除旧的日志文件等
    } else {
        printf("✅ 存储空间充足\n");
    }
    
    printf("\n=== 维护完成 ===\n");
}
```

## 🛠️ 完整示例程序

```c
#include "user_app_flash.h"
#include <stdio.h>
#include <string.h>

// BMS数据结构定义
typedef struct {
    uint16_t cell_voltages[16];
    uint16_t pack_voltage;
    int16_t current;
    int8_t temperatures[4];
    uint8_t soc;
    uint32_t timestamp;
} bms_realtime_data_t;

void demo_bms_fat16_system()
{
    printf("=== BMS FAT16 文件系统演示 ===\n\n");
    
    // 1. 系统初始化
    printf("1. 初始化文件系统\n");
    if (FAT16_Init() != 0) {
        printf("❌ 初始化失败!\n");
        return;
    }
    printf("✅ 初始化成功!\n\n");
    
    // 2. 保存BMS实时数据
    printf("2. 保存BMS实时数据\n");
    bms_realtime_data_t bms_data = {
        .cell_voltages = {3300, 3310, 3305, 3298, 3302, 3307, 3295, 3301,
                         3299, 3304, 3306, 3297, 3303, 3308, 3296, 3300},
        .pack_voltage = 52800,
        .current = 2500,
        .temperatures = {25, 26, 24, 25},
        .soc = 75,
        .timestamp = 1234567890
    };
    
    FAT16_WriteFile("bms_rt.bin", (unsigned char*)&bms_data, sizeof(bms_data));
    printf("BMS实时数据保存完成\n\n");
    
    // 3. 配置文件操作
    printf("3. 配置文件操作\n");
    unsigned char config_data[] = {0x01, 0x02, 0x10, 0x00, 0x68, 0x00}; // 版本1, 16串, 52V系统
    FAT16_SaveConfig(config_data, sizeof(config_data));
    
    unsigned char loaded_config[10];
    int config_size = FAT16_LoadConfig(loaded_config, sizeof(loaded_config));
    printf("配置文件: %d 字节读取\n\n", config_size);
    
    // 4. 日志记录
    printf("4. 系统日志记录\n");
    FAT16_AppendLog((unsigned char*)"[SYSTEM] BMS started\n", 21);
    FAT16_AppendLog((unsigned char*)"[DATA] Pack voltage: 52.8V\n", 27);
    FAT16_AppendLog((unsigned char*)"[ALARM] High temperature\n", 25);
    printf("日志记录完成\n\n");
    
    // 5. 文件查询
    printf("5. 文件系统状态\n");
    printf("存储使用率: %d%%\n", FAT16_GetUsagePercent());
    
    char file_list[16][13];
    int file_count = FAT16_ListFiles(file_list, 16);
    printf("文件列表 (%d 个):\n", file_count);
    for (int i = 0; i < file_count; i++) {
        printf("  - %s (%d 字节)\n", 
               file_list[i], FAT16_GetFileSize(file_list[i]));
    }
    printf("\n");
    
    // 6. 健康检查
    printf("6. 系统健康检查\n");
    if (FAT16_FlashHealthCheck() == 0) {
        printf("✅ 系统健康状态良好\n");
    } else {
        printf("❌ 系统健康检查失败\n");
    }
    
    printf("\n=== 演示完成 ===\n");
}

int main()
{
    demo_bms_fat16_system();
    return 0;
}
```

## ⚠️ 注意事项和限制

### 🔍 文件名限制
- 文件名最大长度：8个字符（不包括扩展名）
- 扩展名最大长度：3个字符
- 支持格式：`filename.ext` 或 `filename`
- 不支持中文文件名和特殊字符 `\/:*?"<>|`

### 💾 存储限制
- 最大文件数量：16个文件
- 单文件最大：16KB（受簇链数组限制）
- 总存储空间：192KB（0x40000-0x70000）
- 簇大小：1KB（1024字节）

### 🧠 内存使用
- **无动态内存分配**：完全避免 malloc/free
- **栈使用量可预测**：最大约4KB（一个扇区缓冲区）
- **缓存优化**：256字节FAT表缓存
- **日志缓冲区**：1KB固定大小

### 🔧 性能特性
- 自动Flash健康检查（每100次写入）
- FAT表缓存机制
- 页对齐优化写入
- 智能簇分配算法

### 🛡️ 错误处理
- 参数验证和边界检查
- Flash健康监控
- 写入验证（可选编译）
- 自动重试和恢复机制

### 📝 最佳实践

1. **初始化**: 系统启动时调用 `FAT16_Init()`
2. **健康监控**: 定期调用 `FAT16_FlashHealthCheck()`
3. **存储管理**: 监控使用率，及时清理不需要的文件
4. **数据保护**: 重要数据建议多份备份
5. **日志管理**: 使用 `FAT16_AppendLog()` 进行系统日志记录
6. **配置管理**: 使用专用接口管理配置文件
7. **错误检查**: 始终检查函数返回值

### 🚫 避免事项

1. **不要**超过16个文件限制
2. **不要**创建过大的单个文件（>16KB）
3. **不要**使用非法文件名字符
4. **不要**在中断中执行文件操作
5. **不要**频繁进行大量写入操作

---

## 📞 技术支持

### 🔧 故障排除

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 初始化失败 | Flash硬件问题 | 检查硬件连接，调用健康检查 |
| 写入失败 | 存储空间不足 | 清理文件，检查使用率 |
| 文件不存在 | 文件名错误 | 检查文件名格式，使用ListFiles |
| 读取失败 | 文件损坏 | 重新写入文件，检查Flash健康 |

### 📋 调试建议

1. 使用 `FAT16_FlashHealthCheck()` 检查Flash状态
2. 使用 `FAT16_ListFiles()` 查看实际文件
3. 使用 `FAT16_GetUsagePercent()` 监控存储使用
4. 检查函数返回值进行错误诊断

更多技术细节请参考源代码 `user_app_flash.c` 和头文件 `user_app_flash.h`。 