#include "tl_common.h"
#include "sh367601xb_parser.h"
#include "sh367601xb.h"

/* ==========================================数据解析模块实现========================================== */

/**
 * @brief 解析ROM寄存器数据
 * @param self 设备实例指针
 * @param data ROM数据缓冲区，长度应为21字节
 * @note 将接收到的ROM原始数据解析到设备ROM结构体中
 */
void sh367601b_parser_parse_rom(User_App_Sh3607601x_Rom_TypeDef* rom, unsigned char *data)
{
    if (data == NULL) return;
    
    /* 00H */
    rom->id = data[0];

    /* 01H */
    rom->enmosr = (data[1] >> 0x07) & 0x01;
    rom->chys   = (data[1] >> 0x06) & 0x01;
    rom->tc     = (data[1] >> 0x04) & 0x03;
    rom->cn     = (data[1] >> 0x00) & 0x0F;

    /* 02H */
    rom->bals  = (data[2] >> 0x07) & 0x01;
    rom->chs   = (data[2] >> 0x06) & 0x01;
    rom->ocra  = (data[2] >> 0x05) & 0x01;
    rom->eovr  = (data[2] >> 0x04) & 0x01;
    rom->euvr  = (data[2] >> 0x03) & 0x01;
    rom->eow   = (data[2] >> 0x02) & 0x01;
    rom->eot3  = (data[2] >> 0x01) & 0x01;
    rom->enmos = (data[2] >> 0x00) & 0x01;

    /* 03H,04H,05H */
    rom->ovt = (data[3] >> 0x06) & 0x03;
    rom->ov  = ((data[3] & 0x3F) << 0x04) | ((data[4] >> 4) & 0x0F);
    rom->ovr = ((data[4] & 0x01) << 0x08) | data[5];

    /* 06H */
    rom->uvr = data[6];

    /* 07H,08H */
    rom->lov  = (data[7] >> 0x05) & 0x07;
    rom->balt = (data[7] >> 0x04) & 0x01;
    rom->uvt  = (data[7] >> 0x01) & 0x03;
    rom->uv   = ((data[7] & 0x01) << 0x08) | data[8];

    /* 09H */
    rom->balv = data[9];

    /* 0AH */
    rom->bald  = (data[10] >> 0x06) & 0x03;
    rom->ocd1v = (data[10] >> 0x02) & 0x0F;
    rom->ocd1t = (data[10] >> 0x00) & 0x03;

    /* 0BH */
    rom->sct   = (data[11] >> 0x05) & 0x03;
    rom->ocd2v = (data[11] >> 0x02) & 0x07;
    rom->ocd2t = (data[11] >> 0x00) & 0x03;

    /* 0CH */
    rom->occv = (data[12] >> 0x02) & 0x1F;
    rom->occt = (data[12] >> 0x00) & 0x03;

    /* 0DH */
    rom->otc   = data[13];
    /* 0EH */
    rom->otcr = data[14];
    /* 0FH */
    rom->otd   = data[15];
    /* 10H */
    rom->otdr  = data[16];
    /* 11H */
    rom->utc   = data[17];
    /* 12H */
    rom->utcr  = data[18];
    /* 13H */
    rom->utd   = data[19];
    /* 14H */
    rom->utdr  = data[20];
}

/**
 * @brief 解析RAM寄存器数据
 * @param self 设备实例指针
 * @param data RAM数据缓冲区，长度应为46字节
 * @note 将接收到的RAM原始数据解析到设备RAM结构体中，包含电池电压、温度、电流等传感器数据
 */
void sh367601b_parser_parse_ram(User_App_Sh3607601x_Ram_TypeDef* ram, unsigned char *data)
{
    if (data == NULL) return;
    
    /* 40H */
    ram->l0v  = (data[0] >> 0x07) & 0x01;
    ram->ow   = (data[0] >> 0x06) & 0x01;
    ram->sc   = (data[0] >> 0x05) & 0x01;
    ram->occ  = (data[0] >> 0x04) & 0x01;
    ram->ocd1 = (data[0] >> 0x03) & 0x01;
    ram->ocd2 = (data[0] >> 0x02) & 0x01;
    ram->uv   = (data[0] >> 0x01) & 0x01;
    ram->ov   = (data[0] >> 0x00) & 0x01;

    /* 41H */
    ram->oti = (data[1] >> 0x04) & 0x01;
    ram->otd = (data[1] >> 0x03) & 0x01;
    ram->utd = (data[1] >> 0x02) & 0x01;
    ram->otc = (data[1] >> 0x01) & 0x01;
    ram->utc = (data[1] >> 0x00) & 0x01;

    /* 42H */
    ram->bal     = (data[2] >> 0x07) & 0x01;
    ram->pd      = (data[2] >> 0x06) & 0x01;
    ram->ctld    = (data[2] >> 0x05) & 0x01;
    ram->pro     = (data[2] >> 0x04) & 0x01;
    ram->chging  = (data[2] >> 0x03) & 0x01;
    ram->dsging  = (data[2] >> 0x02) & 0x01;
    ram->chg_fet = (data[2] >> 0x01) & 0x01;
    ram->dsg_fet = (data[2] >> 0x00) & 0x01;

    /* 43H */
    ram->l0v_flg  = (data[3] >> 0x07) & 0x01;
    ram->ow_flg   = (data[3] >> 0x06) & 0x01;
    ram->sc_flg   = (data[3] >> 0x05) & 0x01;
    ram->occ_flg  = (data[3] >> 0x04) & 0x01;
    ram->ocd1_flg = (data[3] >> 0x03) & 0x01;
    ram->ocd2_flg = (data[3] >> 0x02) & 0x01;
    ram->uv_flg   = (data[3] >> 0x01) & 0x01;
    ram->ov_flg   = (data[3] >> 0x00) & 0x01;

    /* 44H */
    ram->rst_flg = (data[4] >> 0x05) & 0x01;
    ram->oti_flg = (data[4] >> 0x04) & 0x01;
    ram->otd_flg = (data[4] >> 0x03) & 0x01;
    ram->utd_flg = (data[4] >> 0x02) & 0x01;
    ram->otc_flg = (data[4] >> 0x01) & 0x01;
    ram->utc_flg = (data[4] >> 0x00) & 0x01;

    /* 45H,46H */
    ram->temp1   = (data[5] << 8) | data[6];
    /* 47H,48H */
    ram->temp2   = (data[7] << 8) | data[8];
    /* 49H,4AH */
    ram->temp3   = (data[9] << 8) | data[10];
    /* 4BH,4CH */
    ram->tempn   = (data[11] << 8) | data[12];

    /* 4DH, 4EH */
    ram->cur   = ((data[13] << 8) | data[14]);
    /* 4FH,6EH */
    ram->cell1   = (data[15] << 8) | data[16];
    ram->cell2   = (data[17] << 8) | data[18];
    ram->cell3   = (data[19] << 8) | data[20];
    ram->cell4   = (data[21] << 8) | data[22];
    ram->cell5   = (data[23] << 8) | data[24];
    ram->cell6   = (data[25] << 8) | data[26];
    ram->cell7   = (data[27] << 8) | data[28];
    ram->cell8   = (data[29] << 8) | data[30];
    ram->cell9   = (data[31] << 8) | data[32];
    ram->cell10   = (data[33] << 8) | data[34];
    ram->cell11   = (data[35] << 8) | data[36];
    ram->cell12   = (data[37] << 8) | data[38];
    ram->cell13   = (data[39] << 8) | data[40];
    ram->cell14   = (data[41] << 8) | data[42];
    ram->cell15   = (data[43] << 8) | data[44];
    ram->cell16   = (data[45] << 8) | data[46];
}

/**
 * @brief 打印ROM配置信息
 * @param self 设备实例指针
 * @note 以格式化的方式打印所有ROM配置参数，包括保护阈值、延时设置等，默认打印第0个芯片的数据
 */
void sh367601b_parser_print_rom(SH367601B_Device* self)
{
    if (self == NULL) return;

    /* 默认操作第0个芯片的ROM数据 */
    User_App_Sh3607601x_Rom_TypeDef* rom = &self->rom[0];

    printf("=== ROM Configuration (Chip 0) ===\n");
    printf("ID: 0x%02X\n", rom->id);
    printf("ENMOSR: %d, CHYS: %d, TC: %d, CN: %d\n",
           rom->enmosr, rom->chys, rom->tc, rom->cn);
    printf("BALS: %d, CHS: %d, OCRA: %d\n",
           rom->bals, rom->chs, rom->ocra);
    printf("EOVR: %d, EUVR: %d, EOW: %d, EOT3: %d, ENMOS: %d\n",
           rom->eovr, rom->euvr, rom->eow, rom->eot3, rom->enmos);
    printf("OVT: %d, OV: %d mV, OVR: %d mV\n",
           rom->ovt, self->converter.method.ov_to_voltage(rom->ov),
           self->converter.method.ovr_to_voltage(rom->ovr));
    printf("UVR: %d mV, LOV: %d mV\n",
           self->converter.method.uvr_to_voltage(rom->uvr),
           self->converter.method.lov_to_voltage(rom->lov));
    printf("BALT: %d, UVT: %d, UV: %d mV\n",
           rom->balt, rom->uvt, self->converter.method.uv_to_voltage(rom->uv));
    printf("BALV: %d mV, BALD: %d\n",
           self->converter.method.balv_to_voltage(rom->balv), rom->bald);
    printf("OCD1V: %d mV, OCD1T: %d, SCT: %d\n",
           self->converter.method.ocd1v_to_voltage(rom->ocd1v), rom->ocd1t, rom->sct);
    printf("OCD2V: %d mV, OCD2T: %d\n",
           self->converter.method.ocd2v_to_voltage(rom->ocd2v), rom->ocd2t);
    printf("OCCV: %d mV, OCCT: %d\n",
           self->converter.method.occv_to_voltage(rom->occv), rom->occt);
    printf("Temperature Protection: OTC=%d, OTCR=%d, OTD=%d, OTDR=%d\n",
           rom->otc, rom->otcr, rom->otd, rom->otdr);
    printf("Temperature Protection: UTC=%d, UTCR=%d, UTD=%d, UTDR=%d\n",
           rom->utc, rom->utcr, rom->utd, rom->utdr);
}

/**
 * @brief 打印RAM传感器数据
 * @param self 设备实例指针
 * @note 以格式化的方式打印所有传感器数据，包括保护状态、电池电压、温度、电流等，默认打印第0个芯片的数据
 */
void sh367601b_parser_print_ram(SH367601B_Device* self)
{
    if (self == NULL) return;

    /* 默认操作第0个芯片的RAM数据 */
    User_App_Sh3607601x_Ram_TypeDef* ram = &self->ram[0];

    printf("=== RAM Sensor Data (Chip 0) ===\n");

    /* 保护状态信息 (40H) */
    printf("Protection Status:\n");
    printf("  L0V: %d, OW: %d, SC: %d, OCC: %d\n",
           ram->l0v, ram->ow, ram->sc, ram->occ);
    printf("  OCD1: %d, OCD2: %d, UV: %d, OV: %d\n",
           ram->ocd1, ram->ocd2, ram->uv, ram->ov);

    /* 温度保护状态 (41H) */
    printf("Temperature Protection:\n");
    printf("  OTI: %d, OTD: %d, UTD: %d, OTC: %d, UTC: %d\n",
           ram->oti, ram->otd, ram->utd, ram->otc, ram->utc);

    /* 系统状态 (42H) */
    printf("System Status:\n");
    printf("  BAL: %d, PD: %d, CTLD: %d, PRO: %d\n",
           ram->bal, ram->pd, ram->ctld, ram->pro);
    printf("  CHGING: %d, DSGING: %d, CHG_FET: %d, DSG_FET: %d\n",
           ram->chging, ram->dsging, ram->chg_fet, ram->dsg_fet);

    /* 保护标志位 (43H) */
    printf("Protection Flags:\n");
    printf("  L0V_FLG: %d, OW_FLG: %d, SC_FLG: %d, OCC_FLG: %d\n",
           ram->l0v_flg, ram->ow_flg, ram->sc_flg, ram->occ_flg);
    printf("  OCD1_FLG: %d, OCD2_FLG: %d, UV_FLG: %d, OV_FLG: %d\n",
           ram->ocd1_flg, ram->ocd2_flg, ram->uv_flg, ram->ov_flg);

    /* 温度保护标志位 (44H) */
    printf("Temperature Flags:\n");
    printf("  RST_FLG: %d, OTI_FLG: %d, OTD_FLG: %d, UTD_FLG: %d, OTC_FLG: %d, UTC_FLG: %d\n",
           ram->rst_flg, ram->oti_flg, ram->otd_flg,
           ram->utd_flg, ram->otc_flg, ram->utc_flg);

    /* 温度数据 (45H-4CH) */
    printf("Temperatures (Raw/Converted):\n");
    printf("  TEMP1: 0x%04X (%d), TEMP2: 0x%04X (%d)\n",
           ram->temp1, self->tool.method.calc_external_temp(ram->temp1),
           ram->temp2, self->tool.method.calc_external_temp(ram->temp2));
    printf("  TEMP3: 0x%04X (%d°C), TEMPN: 0x%04X (%d°C)\n",
           ram->temp3, self->tool.method.calc_external_temp(ram->temp3),
           ram->tempn, self->tool.method.calc_external_temp(ram->tempn));

    /* 电流数据 (4DH-4EH) */
    printf("Current: 0x%04X (%d mA)\n",
           ram->cur, self->tool.method.calc_current_from_adc(ram->cur, 1.0f, 90.0f, 26214.4f));

    /* 电池电压数据 (4FH-6EH) */
    printf("Cell Voltages (Raw/mV):\n");
    printf("  Cell1-4:   0x%04X(%d), 0x%04X(%d), 0x%04X(%d), 0x%04X(%d)\n",
           ram->cell1, ram->cell1, ram->cell2, ram->cell2,
           ram->cell3, ram->cell3, ram->cell4, ram->cell4);
    printf("  Cell5-8:   0x%04X(%d), 0x%04X(%d), 0x%04X(%d), 0x%04X(%d)\n",
           ram->cell5, ram->cell5, ram->cell6, ram->cell6,
           ram->cell7, ram->cell7, ram->cell8, ram->cell8);
    printf("  Cell9-12:  0x%04X(%d), 0x%04X(%d), 0x%04X(%d), 0x%04X(%d)\n",
           ram->cell9, ram->cell9, ram->cell10, ram->cell10,
           ram->cell11, ram->cell11, ram->cell12, ram->cell12);
    printf("  Cell13-16: 0x%04X(%d), 0x%04X(%d), 0x%04X(%d), 0x%04X(%d)\n",
           ram->cell13, ram->cell13, ram->cell14, ram->cell14,
           ram->cell15, ram->cell15, ram->cell16, ram->cell16);
}
