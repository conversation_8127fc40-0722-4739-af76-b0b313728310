#ifndef SH36760_CHIP_H
#define SH36760_CHIP_H

#include "sh367601xb.h"

/* 前向声明队列结构 */
struct Queue;
typedef struct Queue Queue;

/**
 * @file sh36760_chip.h
 * @brief SH36760芯片模块 - 基于函数指针的芯片操作接口
 * @version 1.0
 * @date 2025
 *
 * 本文件提供了SH36760芯片的统一操作接口，通过函数指针实现：
 * - 芯片切换操作
 * - ROM数据写入操作
 * - 芯片数量管理
 * - 批量写入流程控制
 */

/* ==========================================SH36760芯片模块接口定义========================================== */

/**
 * @brief SH36760芯片操作接口结构体
 * @note 提供统一的芯片操作接口，通过函数指针实现不同芯片的操作
 */
typedef struct {
    /* 芯片切换操作 */
    int (*switch_to_chip)(SH367601B_Device* device, unsigned char chip_index);
    
    /* ROM数据写入操作（基于队列机制） */
    int (*write_rom_for_chip)(SH367601B_Device* device, Queue* q);
    
    /* 芯片数量设置 */
    int (*set_chip_count)(SH367601B_Device* device, unsigned char chip_count);
    
    /* 批量写ROM流程（自动遍历所有芯片） */
    int (*start_batch_write_rom)(SH367601B_Device* device, Queue* q);
    
    /* 扩展接口：获取当前芯片索引 */
    unsigned char (*get_current_chip_index)(SH367601B_Device* device);
    
    /* 扩展接口：获取芯片数量 */
    unsigned char (*get_chip_count)(SH367601B_Device* device);
    
    /* 扩展接口：检查写ROM流程状态 */
    unsigned char (*is_write_rom_in_progress)(SH367601B_Device* device);
    
} SH36760_ChipInterface;

/**
 * @brief SH36760芯片模块管理器
 * @note 封装芯片操作接口和相关数据，提供完整的芯片管理功能
 */
typedef struct {
    /* 芯片操作接口 */
    SH36760_ChipInterface* interface;
    
    /* 模块信息 */
    struct {
        const char* module_name;        /* 模块名称 */
        const char* version;            /* 版本信息 */
        unsigned char is_initialized;   /* 初始化状态 */
    } info;
    
    /* 模块方法 */
    struct {
        /* 初始化模块 */
        int (*init)(void);
        
        /* 获取模块信息 */
        void (*get_info)(void);
        
        /* 重置模块状态 */
        void (*reset)(void);
    } methods;
    
} SH36760_ChipManager;

/* ==========================================函数声明========================================== */

/**
 * @brief 获取SH36760芯片操作接口
 * @return SH36760芯片接口结构体指针
 * @note 返回包含所有芯片操作函数指针的接口结构体
 */
extern SH36760_ChipInterface* sh36760_get_chip_interface(void);

/**
 * @brief 获取SH36760芯片管理器实例
 * @return SH36760芯片管理器指针
 * @note 返回完整的芯片管理器实例，包含接口和管理方法
 */
extern SH36760_ChipManager* sh36760_get_chip_manager(void);

/**
 * @brief 初始化SH36760芯片模块
 * @return 0=成功，-1=失败
 * @note 初始化芯片模块，设置函数指针和默认参数
 */
extern int sh36760_chip_module_init(void);

/* ==========================================扩展功能函数声明========================================== */

/**
 * @brief 获取当前芯片索引
 * @param device 设备实例指针
 * @return 当前芯片索引
 */
extern unsigned char sh36760_get_current_chip_index(SH367601B_Device* device);

/**
 * @brief 获取芯片数量
 * @param device 设备实例指针
 * @return 芯片数量
 */
extern unsigned char sh36760_get_chip_count(SH367601B_Device* device);

/**
 * @brief 检查写ROM流程状态
 * @param device 设备实例指针
 * @return 1=写ROM流程进行中，0=空闲状态
 */
extern unsigned char sh36760_is_write_rom_in_progress(SH367601B_Device* device);

/* ==========================================使用示例========================================== */

/*
使用示例：

// 1. 获取芯片接口
SH36760_ChipInterface* chip_interface = sh36760_get_chip_interface();

// 2. 使用函数指针调用芯片操作
SH367601B_Device device;
Queue queue;

// 切换到芯片1
chip_interface->switch_to_chip(&device, 1);

// 设置芯片数量为4
chip_interface->set_chip_count(&device, 4);

// 写入ROM数据
chip_interface->write_rom_for_chip(&device, &queue);

// 启动批量写ROM流程
chip_interface->start_batch_write_rom(&device, &queue);

// 3. 使用芯片管理器
SH36760_ChipManager* manager = sh36760_get_chip_manager();
manager->methods.init();
manager->methods.get_info();
*/

#endif /* SH36760_CHIP_H */
