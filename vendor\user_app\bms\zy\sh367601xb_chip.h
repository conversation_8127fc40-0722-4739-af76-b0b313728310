#ifndef SH36760_CHIP_H
#define SH36760_CHIP_H

#include "sh367601xb.h"
#include "../../list/list_type.h"

/* 切换到指定芯片 */
extern void sh36760_switch_to_chip(SH367601B_Device* device, unsigned char chip_index);
/* 写入ROM数据（基于队列机制） */
extern void sh36760_write_rom_for_chip(SH367601B_Device* device, Queue* q);
/* 设置芯片数量 */
extern void sh36760_set_chip_count(SH367601B_Device* device, unsigned char chip_count);
/* 启动批量写ROM流程（自动遍历所有芯片） */
extern void sh36760_start_batch_write_rom(SH367601B_Device* device, Queue* q);
/* 设置单个芯片类型 */
extern void sh36760_set_chip_type(SH367601B_Device* device, unsigned char chip_index, SH367601B_ChipType chip_type);
/* 获取单个芯片类型 */
extern SH367601B_ChipType sh36760_get_chip_type(SH367601B_Device* device, unsigned char chip_index);
/* 获取单个芯片最大串数 */
extern unsigned char sh36760_get_chip_max_cells(SH367601B_Device* device, unsigned char chip_index);

/* 处理复位任务中的芯片切换逻辑 */
extern int sh36760_handle_reset_task_chip_switching(SH367601B_Device* device, Queue* q);

/* 处理RAM读取任务中的芯片切换逻辑 */
extern int sh36760_handle_ram_read_chip_switching(SH367601B_Device* device, Queue* q, bool is_rom_task);

/* 设置单个芯片的硬件切换函数 */
extern void sh36760_set_chip_switch_function(SH367601B_Device* device, unsigned char chip_index, void (*switch_func)(void));


#endif /* SH36760_CHIP_H */
