#ifndef SH367601XB_CHIP_H
#define SH367601XB_CHIP_H

#include "sh367601xb.h"

/* 前向声明队列结构 */
struct Queue;
typedef struct Queue Queue;

/**
 * @brief 切换到指定芯片（设置当前芯片索引并执行IO切换）
 * @param device 设备实例指针
 * @param chip_index 目标芯片索引 (0-MAX_CHIP_COUNT-1)
 * @return 0=成功，-1=失败
 */
extern int sh367601b_switch_to_chip(SH367601B_Device* device, unsigned char chip_index);

/**
 * @brief 写入ROM数据（基于队列机制）
 * @param device 设备实例指针
 * @param q 队列指针（来自主程序的全局队列）
 * @return 0=成功，-1=失败
 */
extern int sh367601b_write_rom_for_chip(SH367601B_Device* device, Queue* q);

/**
 * @brief 设置芯片数量
 * @param device 设备实例指针
 * @param chip_count 芯片数量 (1-MAX_CHIP_COUNT)
 * @return 0=成功，-1=失败
 */
extern int sh367601b_set_chip_count(SH367601B_Device* device, unsigned char chip_count);

/**
 * @brief 启动批量写ROM流程（自动遍历所有芯片）
 * @param device 设备实例指针
 * @param q 队列指针（来自主程序的全局队列）
 * @return 0=成功，-1=失败
 */
extern int sh367601b_start_batch_write_rom(SH367601B_Device* device, Queue* q);

/* ==========================================SH36760芯片模块函数指针接口========================================== */

/**
 * @brief SH36760芯片模块函数指针结构体
 * @note 提供统一的芯片操作接口，可以通过函数指针调用不同芯片的实现
 */
typedef struct {
    /* 切换到指定芯片 */
    int (*switch_to_chip)(SH367601B_Device* device, unsigned char chip_index);
    /* 写入ROM数据（基于队列机制） */
    int (*write_rom_for_chip)(SH367601B_Device* device, Queue* q);
    /* 设置芯片数量 */
    int (*set_chip_count)(SH367601B_Device* device, unsigned char chip_count);
    /* 启动批量写ROM流程（自动遍历所有芯片） */
    int (*start_batch_write_rom)(SH367601B_Device* device, Queue* q);
} SH36760_ChipInterface;

/**
 * @brief 获取SH36760芯片接口实例
 * @return SH36760芯片接口结构体指针
 * @note 返回包含所有芯片操作函数指针的接口结构体
 */
extern SH36760_ChipInterface* sh36760_get_chip_interface(void);

#endif /* SH367601XB_CHIP_H */
