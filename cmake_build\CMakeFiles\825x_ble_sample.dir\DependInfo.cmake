
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "ASM"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_ASM
  "D:/Telink_Project/BMS_Base/boot/B85/cstartup_825x.S" "D:/Telink_Project/BMS_Base/cmake_build/CMakeFiles/825x_ble_sample.dir/boot/B85/cstartup_825x.S.obj"
  "D:/Telink_Project/BMS_Base/div_mod.S" "D:/Telink_Project/BMS_Base/cmake_build/CMakeFiles/825x_ble_sample.dir/div_mod.S.obj"
  )
set(CMAKE_ASM_COMPILER_ID "GNU")

# The include file search paths:
set(CMAKE_ASM_TARGET_INCLUDE_PATH
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "D:/Telink_Project/BMS_Base/application/app/usbaud.c" "CMakeFiles/825x_ble_sample.dir/application/app/usbaud.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/application/app/usbaud.c.obj.d"
  "D:/Telink_Project/BMS_Base/application/app/usbcdc.c" "CMakeFiles/825x_ble_sample.dir/application/app/usbcdc.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/application/app/usbcdc.c.obj.d"
  "D:/Telink_Project/BMS_Base/application/app/usbkb.c" "CMakeFiles/825x_ble_sample.dir/application/app/usbkb.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/application/app/usbkb.c.obj.d"
  "D:/Telink_Project/BMS_Base/application/app/usbmouse.c" "CMakeFiles/825x_ble_sample.dir/application/app/usbmouse.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/application/app/usbmouse.c.obj.d"
  "D:/Telink_Project/BMS_Base/application/audio/adpcm.c" "CMakeFiles/825x_ble_sample.dir/application/audio/adpcm.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/application/audio/adpcm.c.obj.d"
  "D:/Telink_Project/BMS_Base/application/audio/gl_audio.c" "CMakeFiles/825x_ble_sample.dir/application/audio/gl_audio.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/application/audio/gl_audio.c.obj.d"
  "D:/Telink_Project/BMS_Base/application/audio/tl_audio.c" "CMakeFiles/825x_ble_sample.dir/application/audio/tl_audio.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/application/audio/tl_audio.c.obj.d"
  "D:/Telink_Project/BMS_Base/application/keyboard/keyboard.c" "CMakeFiles/825x_ble_sample.dir/application/keyboard/keyboard.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/application/keyboard/keyboard.c.obj.d"
  "D:/Telink_Project/BMS_Base/application/print/putchar.c" "CMakeFiles/825x_ble_sample.dir/application/print/putchar.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/application/print/putchar.c.obj.d"
  "D:/Telink_Project/BMS_Base/application/print/u_printf.c" "CMakeFiles/825x_ble_sample.dir/application/print/u_printf.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/application/print/u_printf.c.obj.d"
  "D:/Telink_Project/BMS_Base/application/usbstd/usb.c" "CMakeFiles/825x_ble_sample.dir/application/usbstd/usb.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/application/usbstd/usb.c.obj.d"
  "D:/Telink_Project/BMS_Base/application/usbstd/usbdesc.c" "CMakeFiles/825x_ble_sample.dir/application/usbstd/usbdesc.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/application/usbstd/usbdesc.c.obj.d"
  "D:/Telink_Project/BMS_Base/common/sdk_version.c" "CMakeFiles/825x_ble_sample.dir/common/sdk_version.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/common/sdk_version.c.obj.d"
  "D:/Telink_Project/BMS_Base/common/string.c" "CMakeFiles/825x_ble_sample.dir/common/string.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/common/string.c.obj.d"
  "D:/Telink_Project/BMS_Base/common/utility.c" "CMakeFiles/825x_ble_sample.dir/common/utility.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/common/utility.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/adc.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/adc.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/adc.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/aes.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/aes.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/aes.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/analog.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/analog.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/analog.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/audio.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/audio.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/audio.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/bsp.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/bsp.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/bsp.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/clock.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/clock.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/clock.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/driver_ext/ext_calibration.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_calibration.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_calibration.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/driver_ext/ext_misc.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_misc.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/ext_misc.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/driver_ext/rf_pa.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/rf_pa.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/rf_pa.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/driver_ext/software_uart.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/software_uart.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/driver_ext/software_uart.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/emi.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/emi.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/emi.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/flash.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/flash/flash_mid011460c8.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid011460c8.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid011460c8.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/flash/flash_mid1060c8.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1060c8.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1060c8.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/flash/flash_mid13325e.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid13325e.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid13325e.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/flash/flash_mid134051.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid134051.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid134051.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/flash/flash_mid136085.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid136085.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid136085.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/flash/flash_mid1360c8.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360c8.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360c8.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/flash/flash_mid1360eb.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360eb.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1360eb.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/flash/flash_mid14325e.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid14325e.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid14325e.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/flash/flash_mid1460c8.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1460c8.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/flash/flash_mid1460c8.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/gpio.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/gpio.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/gpio.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/i2c.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/i2c.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/i2c.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/lpc.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/lpc.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/lpc.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/qdec.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/qdec.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/qdec.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/s7816.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/s7816.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/s7816.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/spi.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/spi.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/spi.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/timer.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/timer.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/timer.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/uart.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/uart.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/uart.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/usbhw.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/usbhw.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/usbhw.c.obj.d"
  "D:/Telink_Project/BMS_Base/drivers/8258/watchdog.c" "CMakeFiles/825x_ble_sample.dir/drivers/8258/watchdog.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/drivers/8258/watchdog.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/b85m_ble_sample/app.c" "CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/b85m_ble_sample/app_att.c" "CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_att.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_att.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/b85m_ble_sample/app_ui.c" "CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_ui.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/app_ui.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/b85m_ble_sample/main.c" "CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/main.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/b85m_ble_sample/main.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/common/app_buffer.c" "CMakeFiles/825x_ble_sample.dir/vendor/common/app_buffer.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/common/app_buffer.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/common/app_common.c" "CMakeFiles/825x_ble_sample.dir/vendor/common/app_common.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/common/app_common.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/common/battery_check.c" "CMakeFiles/825x_ble_sample.dir/vendor/common/battery_check.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/common/battery_check.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/common/ble_flash.c" "CMakeFiles/825x_ble_sample.dir/vendor/common/ble_flash.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/common/ble_flash.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/common/blt_fw_sign.c" "CMakeFiles/825x_ble_sample.dir/vendor/common/blt_fw_sign.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/common/blt_fw_sign.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/common/blt_led.c" "CMakeFiles/825x_ble_sample.dir/vendor/common/blt_led.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/common/blt_led.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/common/blt_soft_timer.c" "CMakeFiles/825x_ble_sample.dir/vendor/common/blt_soft_timer.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/common/blt_soft_timer.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/common/custom_pair.c" "CMakeFiles/825x_ble_sample.dir/vendor/common/custom_pair.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/common/custom_pair.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/common/flash_fw_check.c" "CMakeFiles/825x_ble_sample.dir/vendor/common/flash_fw_check.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/common/flash_fw_check.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/common/flash_prot.c" "CMakeFiles/825x_ble_sample.dir/vendor/common/flash_prot.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/common/flash_prot.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/common/simple_sdp.c" "CMakeFiles/825x_ble_sample.dir/vendor/common/simple_sdp.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/common/simple_sdp.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/common/tlkapi_debug.c" "CMakeFiles/825x_ble_sample.dir/vendor/common/tlkapi_debug.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/common/tlkapi_debug.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/common/user_config.c" "CMakeFiles/825x_ble_sample.dir/vendor/common/user_config.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/common/user_config.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/user_app/agreement/agreement.c" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/agreement.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/agreement.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/user_app/agreement/rzn/rzn_agreement.c" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/rzn/rzn_agreement.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/agreement/rzn/rzn_agreement.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/user_app/at_cmd/app_at_cmd.c" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/at_cmd/app_at_cmd.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/at_cmd/app_at_cmd.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/user_app/att_ble/app_ble.c" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/att_ble/app_ble.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/att_ble/app_ble.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/user_app/bms/bms_data.c" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/user_app/bms/bms_data_tool.c" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data_tool.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/bms_data_tool.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/user_app/bms/zy/sh367601xb.c" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/user_app/bms/zy/sh367601xb_chip.c" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_chip.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_chip.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/user_app/bms/zy/sh367601xb_communication.c" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_communication.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_communication.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/user_app/bms/zy/sh367601xb_config.c" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_config.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_config.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/user_app/bms/zy/sh367601xb_converter.c" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_converter.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_converter.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/user_app/bms/zy/sh367601xb_parser.c" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_parser.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_parser.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/user_app/bms/zy/sh367601xb_tool.c" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_tool.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/bms/zy/sh367601xb_tool.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/user_app/flash/user_app_flash.c" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/flash/user_app_flash.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/flash/user_app_flash.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/user_app/list/queue/queue.c" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/list/queue/queue.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/list/queue/queue.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/user_app/system_main/app_mian.c" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/system_main/app_mian.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/system_main/app_mian.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/user_app/uart/app_usart.c" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/uart/app_usart.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/uart/app_usart.c.obj.d"
  "D:/Telink_Project/BMS_Base/vendor/user_app/user_app_main.c" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_main.c.obj" "gcc" "CMakeFiles/825x_ble_sample.dir/vendor/user_app/user_app_main.c.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
