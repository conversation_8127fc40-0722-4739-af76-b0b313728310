#ifndef BMS_DATA_H_
#define BMS_DATA_H_

/**
 * @file bms_data.h
 * @brief BMS数据管理系统面向对象架构设计
 * @version 1.0
 * @date 2025
 */


#include "types.h"


/* ==========================================数据结构定义========================================== */


/**
 * @brief 电流状态枚举
 */
typedef enum 
{
    CURRENT_STATE_IDLE = 0,     /* 空闲状态 */
    CURRENT_STATE_CHARGING,     /* 充电状态 */
    CURRENT_STATE_DISCHARGING   /* 放电状态 */
} CurrentState;

/**
 * @brief 电流数据结构
 */
typedef struct 
{
    int total_current;          /* 总电流，单位：mA */
    int max_charge_current;     /* 最大充电电流，单位：mA */
    int max_discharge_current;  /* 最大放电电流，单位：mA */
    CurrentState current_state; /* 电流状态：空闲/充电/放电 */
} CurrentData;

/**
 * @brief 充放电管理数据结构
 */
typedef struct 
{
    unsigned int cycle_count;           /* 循环次数 */
    unsigned int remaining_capacity;    /* 剩余容量，单位：mAh */
    unsigned int remaining_time;        /* 剩余可用时间，单位：分钟 */
    unsigned int accumulated_charge;    /* 累计充电量，单位：mAh */
    unsigned int accumulated_discharge; /* 累计放电量，单位：mAh */
    unsigned int power;                /* 当前功率，单位：mW */
} ChargeDischargeData;

/**
 * @brief 温度数据结构
 */
typedef struct 
{
    /* 温度传感器数量 */
    unsigned char external_temp_count;   /* 外部温度数量 */
    unsigned char chip_temp_count;       /* 芯片温度数量 */
    unsigned char mos_temp_count;        /* MOS管温度数量 */

    /* 温度值 */
    signed char external_temp[10];      /* 外部温度，单位：℃ */
    signed char max_external_temp;      /* 最大外部温度，单位：℃ */
    signed char min_external_temp;      /* 最小外部温度，单位：℃ */
    signed char chip_temp;              /* 芯片温度，单位：℃ */
    signed char mos_temp;               /* MOS管温度，单位：℃ */
} TemperatureData;

/**
 * @brief 电池状态信息数据结构
 */
typedef struct 
{
    unsigned short soc;              /* SOC电量百分比，0.01%精度，范围0-10000 (0.00%-100.00%) */
    unsigned short soh;              /* SOH健康状态，0.01%精度，范围0-10000 (0.00%-100.00%) */
    unsigned int sampling_time_last;     /* 采样时间（上一次），单位：ms */
    unsigned int sampling_time_curr;     /* 采样时间（新），单位：ms */
} BatteryStateData;

/**
 * @brief 保护参数设置数据结构
 */
typedef struct 
{
    unsigned short overvoltage_protection;          /* 过压保护，单位：mV */
    unsigned short overvoltage_recovery;            /* 过压恢复，单位：mV */
    unsigned short overvoltage_delay;               /* 过压延时*/
    unsigned short undervoltage_protection;         /* 欠压保护，单位：mV */
    unsigned short undervoltage_recovery;           /* 欠压恢复，单位：mV */
    unsigned short undervoltage_delay;              /* 欠压延时*/

    unsigned short discharge_overcurrent1;          /* 放电过流1保护，单位：mA */
    unsigned short discharge_overcurrent1_delay;    /* 放电过流1保护延时 */
    unsigned short discharge_overcurrent2;          /* 放电过流2保护，单位：mA */
    unsigned short discharge_overcurrent2_delay;    /* 放电过流2保护延时 */
    unsigned short charge_overcurrent1;             /* 充电过流1保护，单位：mA */
    unsigned short charge_overcurrent1_delay;       /* 充电过流1保护延时 */

    char charge_high_temp_protection;               /* 充电高温保护，单位：℃ */
    char charge_high_temp_recovery;                 /* 充电高温保护释放，单位：℃ */
    char charge_low_temp_protection;                /* 充电低温保护，单位：℃ */
    char charge_low_temp_recovery;                  /* 充电低温保护释放，单位：℃ */
    char discharge_high_temp_protection;            /* 放电高温保护，单位：℃ */
    char discharge_high_temp_recovery;              /* 放电高温保护释放，单位：℃ */
    char discharge_low_temp_protection;             /* 放电低温保护，单位：℃ */
    char discharge_low_temp_recovery;               /* 放电低温保护释放，单位：℃ */
    unsigned short balance_start_voltage_diff;      /* 均衡开启压差，单位：mV */
    unsigned short balance_start_voltage;           /* 均衡开启电压，单位：mV */
} ProtectionParameters;

/**
 * @brief 自定义参数数据结构
 */
typedef struct 
{
    unsigned int battery_total_capacity;            /* 电池总容量，单位：mAh */
    unsigned int battery_remaining_capacity;        /* 电池剩余容量，单位：mAh */
    unsigned short self_consumption;                /* 自耗电，单位：mA */
} CustomParameters;

/* ==========================================告警管理模块========================================== */

/**
 * @brief 告警管理模块类
 */
typedef struct AlarmManager {
    /* 数据成员 */
    struct 
    {
        bool charge_over_temp;      /* 充电高温保护 */
        bool charge_under_temp;     /* 充电低温保护 */
        bool discharge_over_temp;   /* 放电高温保护 */
        bool discharge_under_temp;  /* 放电低温保护 */
        bool charge_overcurrent;    /* 充电过流 */
        bool discharge_overcurrent1; /* 放电过流1 */
        bool discharge_overcurrent2; /* 放电过流2 */
        bool undervoltage;          /* 欠压保护 */
        bool overvoltage;           /* 过压保护 */
    }data;
    

    /* 方法指针 */
    struct 
    {
        void (*init)(struct AlarmManager* self);
        void (*process_alarm_data)(struct AlarmManager* self, bool charge_over_temp, bool charge_under_temp,
                                  bool discharge_over_temp, bool discharge_under_temp, bool charge_overcurrent,
                                  bool discharge_overcurrent1, bool discharge_overcurrent2, bool undervoltage, bool overvoltage);
    } methods;
} AlarmManager;

/* ==========================================状态管理模块========================================== */

/**
 * @brief 状态管理模块类
 */
typedef struct StatusManager 
{
    /* 数据成员 */
    struct 
    {
        bool balance_status;        /* 均衡状态 */
        bool charge_mos;           /* 充电MOS */
        bool discharge_mos;        /* 放电MOS */
        bool charge_status;        /* 充电状态 */
        bool discharge_status;     /* 放电状态 */
    }data;

    /* 方法指针 */
    struct 
    {
        void (*init)(struct StatusManager* self);
        void (*process_status_data)(struct StatusManager* self, bool balance_status, bool charge_mos,
                                   bool discharge_mos, bool charge_status, bool discharge_status);
    } methods;
} StatusManager;

/* ==========================================电压管理模块========================================== */

/**
 * @brief 电压管理模块类
 */
typedef struct VoltageManager 
{
    /* 数据成员 */
    struct 
    {
        unsigned int total_voltage;       /* 总电压，单位：mV */
        unsigned short cell_voltages[64]; /* 单体电压，单位：mV */
        unsigned short max_voltage;       /* 最大电压，单位：mV */
        unsigned short min_voltage;       /* 最小电压，单位：mV */
        unsigned short average_voltage;   /* 平均电压，单位：mV */
        unsigned short voltage_diff;      /* 电池压差，单位：mV */
        unsigned char battery_count;      /* 电池串数 */
    }data;

    /* 方法指针 */
    struct 
    {
        void (*init)(struct VoltageManager* self);
        void (*process_voltage_data)(struct VoltageManager* self, unsigned short* cells, int battery_count);
    } methods;
} VoltageManager;

/* ==========================================电流管理模块========================================== */

/**
 * @brief 电流管理模块类
 */
typedef struct CurrentManager 
{
    /* 数据成员 */
    struct 
    {
        int total_current;          /* 总电流，单位：mA */
        int max_charge_current;     /* 最大充电电流，单位：mA */
        int max_discharge_current;  /* 最大放电电流，单位：mA */
        CurrentState current_state; /* 电流状态：空闲/充电/放电 */
    }data;

    /* 方法指针 */
    struct 
    {
        void (*init)(struct CurrentManager* self);
        void (*process_current_data)(struct CurrentManager* self, unsigned int current, bool charge_status, bool discharge_status);
    } methods;
} CurrentManager;

/* ==========================================充放电管理模块========================================== */

/**
 * @brief 充放电管理模块类
 */
typedef struct ChargeDischargeManager 
{
    /* 数据成员 */
    struct 
    {
        unsigned int cycle_count;           /* 循环次数 */
        unsigned int remaining_capacity;    /* 剩余容量，单位：mAh */
        unsigned int remaining_time;        /* 剩余可用时间，单位：分钟 */
        unsigned int accumulated_charge;    /* 累计充电量，单位：mAh */
        unsigned int accumulated_discharge; /* 累计放电量，单位：mAh */
        unsigned int power;                /* 当前功率，单位：mW */
    }data;

    /* 方法指针 */
    struct 
    {
        void (*init)(struct ChargeDischargeManager* self);
        void (*process_charge_discharge_data)(struct ChargeDischargeManager* self, int current_ma, bool charge_status, bool discharge_status, unsigned int total_voltage_mv, unsigned int battery_capacity);
    } methods;
} ChargeDischargeManager;

/* ==========================================温度管理模块========================================== */

/**
 * @brief 温度管理模块类
 */
typedef struct TemperatureManager 
{
    /* 数据成员 */
    struct 
    {
        /* 温度传感器数量 */
        unsigned char external_temp_count;   /* 外部温度数量 */
        unsigned char chip_temp_count;       /* 芯片温度数量 */
        unsigned char mos_temp_count;        /* MOS管温度数量 */

        /* 温度值 */
        signed char external_temp[10];      /* 外部温度，单位：℃ */
        signed char max_external_temp;      /* 最大外部温度，单位：℃ */
        signed char min_external_temp;      /* 最小外部温度，单位：℃ */
        signed char chip_temp;              /* 芯片温度，单位：℃ */
        signed char mos_temp;               /* MOS管温度，单位：℃ */
    }data;

    /* 方法指针 */
    struct 
    {
        void (*init)(struct TemperatureManager* self);
        void (*process_temperature_data)(struct TemperatureManager* self, signed char* external_temps, unsigned char temp_count, signed char chip_temp);
    } methods;
} TemperatureManager;

/* ==========================================电池状态管理模块========================================== */

/**
 * @brief 电池状态管理模块类
 */
typedef struct BatteryStateManager 
{
    /* 数据成员 */
    struct 
    {
        unsigned short soc;              /* SOC电量百分比，0.01%精度，范围0-10000 (0.00%-100.00%) */
        unsigned short soh;              /* SOH健康状态，0.01%精度，范围0-10000 (0.00%-100.00%) */
        unsigned int sampling_time_last;     /* 采样时间（上一次），单位：ms */
        unsigned int sampling_time_curr;     /* 采样时间（新），单位：ms */
    }data;

    /* 方法指针 */
    struct 
    {
        void (*init)(struct BatteryStateManager* self);
        void (*process_battery_state_data)(struct BatteryStateManager* self, unsigned int cycle_count, float current_ma, float battery_capacity);
    } methods;
} BatteryStateManager;

/* ==========================================保护参数管理模块========================================== */

/**
 * @brief 保护参数管理模块类
 */
typedef struct ProtectionParameterManager 
{
    /* 数据成员 */
    struct 
    {
        unsigned short overvoltage_protection;          /* 过压保护，单位：mV */
        unsigned short overvoltage_recovery;            /* 过压恢复，单位：mV */
        unsigned short overvoltage_delay;               /* 过压延时*/
        unsigned short undervoltage_protection;         /* 欠压保护，单位：mV */
        unsigned short undervoltage_recovery;           /* 欠压恢复，单位：mV */
        unsigned short undervoltage_delay;              /* 欠压延时*/

        unsigned short discharge_overcurrent1;          /* 放电过流1保护，单位：mA */
        unsigned short discharge_overcurrent1_delay;    /* 放电过流1保护延时 */
        unsigned short discharge_overcurrent2;          /* 放电过流2保护，单位：mA */
        unsigned short discharge_overcurrent2_delay;    /* 放电过流2保护延时 */
        unsigned short charge_overcurrent1;             /* 充电过流1保护，单位：mA */
        unsigned short charge_overcurrent1_delay;       /* 充电过流1保护延时 */

        char charge_high_temp_protection;               /* 充电高温保护，单位：℃ */
        char charge_high_temp_recovery;                 /* 充电高温保护释放，单位：℃ */
        char charge_low_temp_protection;                /* 充电低温保护，单位：℃ */
        char charge_low_temp_recovery;                  /* 充电低温保护释放，单位：℃ */
        char discharge_high_temp_protection;            /* 放电高温保护，单位：℃ */
        char discharge_high_temp_recovery;              /* 放电高温保护释放，单位：℃ */
        char discharge_low_temp_protection;             /* 放电低温保护，单位：℃ */
        char discharge_low_temp_recovery;               /* 放电低温保护释放，单位：℃ */
        unsigned short balance_start_voltage_diff;      /* 均衡开启压差，单位：mV */
        unsigned short balance_start_voltage;           /* 均衡开启电压，单位：mV */
    }data;

    /* 方法指针 */
    struct 
    {
        void (*init)(struct ProtectionParameterManager* self);
        void (*process_protection_data)(struct ProtectionParameterManager* self,
                                      unsigned short overvoltage_protection, unsigned short overvoltage_recovery, unsigned short overvoltage_delay,
                                      unsigned short undervoltage_protection, unsigned short undervoltage_recovery, unsigned short undervoltage_delay,
                                      unsigned short discharge_overcurrent1, unsigned short discharge_overcurrent1_delay,
                                      unsigned short discharge_overcurrent2, unsigned short discharge_overcurrent2_delay,
                                      unsigned short charge_overcurrent1, unsigned short charge_overcurrent1_delay,
                                      char charge_high_temp_protection, char charge_high_temp_recovery,
                                      char charge_low_temp_protection, char charge_low_temp_recovery,
                                      char discharge_high_temp_protection, char discharge_high_temp_recovery,
                                      char discharge_low_temp_protection, char discharge_low_temp_recovery,
                                      unsigned short balance_start_voltage_diff, unsigned short balance_start_voltage);
    } methods;
} ProtectionParameterManager;

/* ==========================================自定义参数管理模块========================================== */

/**
 * @brief 自定义参数管理模块类
 */
typedef struct CustomParameterManager 
{
    /* 数据成员 */
    struct 
    {
        unsigned int battery_total_capacity;            /* 电池总容量，单位：mAh */
        unsigned int battery_remaining_capacity;        /* 电池剩余容量，单位：mAh */
        unsigned short self_consumption;                /* 自耗电，单位：mA */
    }data;

    /* 方法指针 */
    struct 
    {
        void (*init)(struct CustomParameterManager* self);
        void (*process_custom_param_data)(struct CustomParameterManager* self,
                                         unsigned int battery_total_capacity,
                                         unsigned int battery_remaining_capacity,
                                         unsigned short self_consumption);
    } methods;
} CustomParameterManager;

/* ==========================================BMS主数据管理器========================================== */

/**
 * @brief BMS主数据管理器类
 */
typedef struct BMS_DataManager 
{
    /* 数据成员 - 静态分配的管理器实例 */
    AlarmManager alarm_mgr;
    StatusManager status_mgr;
    VoltageManager voltage_mgr;
    CurrentManager current_mgr;
    ChargeDischargeManager charge_discharge_mgr;
    TemperatureManager temp_mgr;
    BatteryStateManager battery_state_mgr;
    ProtectionParameterManager protection_param_mgr;
    CustomParameterManager custom_param_mgr;

    bool is_initialized;
} BMS_DataManager;

/* ==========================================管理器初始化函数声明========================================== */

/**
 * @brief 初始化BMS数据管理器实例（静态分配版本）
 * @param self BMS数据管理器实例指针
 * @return 0=成功，-1=失败
 */
extern int bms_init(BMS_DataManager* self);

#endif /* BMS_DATA_H_ */
