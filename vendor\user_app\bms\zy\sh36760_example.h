#ifndef SH36760_EXAMPLE_H
#define SH36760_EXAMPLE_H

#include "sh36760_chip.h"

/**
 * @file sh36760_example.h
 * @brief SH36760芯片模块使用示例头文件
 * @version 1.0
 * @date 2025
 *
 * 本文件声明了SH36760芯片模块的使用示例函数：
 * - 基本操作示例
 * - 管理器使用示例
 * - 批量操作示例
 * - 完整工作流程示例
 */

/* 前向声明 */
struct Queue;
typedef struct Queue Queue;

/* ==========================================示例函数声明========================================== */

/**
 * @brief SH36760芯片模块基本使用示例
 * @param device 设备实例指针
 * @param queue 队列指针
 * @note 展示基本的芯片操作调用方法
 */
extern void sh36760_basic_usage_example(SH367601B_Device* device, Queue* queue);

/**
 * @brief SH36760芯片模块管理器使用示例
 * @note 展示如何使用芯片管理器的功能
 */
extern void sh36760_manager_usage_example(void);

/**
 * @brief SH36760芯片模块批量写ROM示例
 * @param device 设备实例指针
 * @param queue 队列指针
 * @note 展示批量写ROM操作的完整流程
 */
extern void sh36760_batch_write_example(SH367601B_Device* device, Queue* queue);

/**
 * @brief SH36760芯片模块完整工作流程示例
 * @param device 设备实例指针
 * @param queue 队列指针
 * @note 展示一个完整的芯片操作工作流程
 */
extern void sh36760_complete_workflow_example(SH367601B_Device* device, Queue* queue);

/**
 * @brief SH36760芯片模块所有示例的主函数
 * @param device 设备实例指针
 * @param queue 队列指针
 * @note 运行所有示例函数
 */
extern void sh36760_run_all_examples(SH367601B_Device* device, Queue* queue);

/* ==========================================快速使用指南========================================== */

/*
快速使用指南：

1. 包含头文件：
   #include "sh36760_chip.h"
   #include "sh36760_example.h"

2. 初始化模块：
   sh36760_chip_module_init();

3. 获取接口：
   SH36760_ChipInterface* interface = sh36760_get_chip_interface();

4. 使用函数指针调用：
   interface->switch_to_chip(device, chip_index);
   interface->write_rom_for_chip(device, queue);
   interface->set_chip_count(device, count);
   interface->start_batch_write_rom(device, queue);

5. 运行示例：
   sh36760_run_all_examples(device, queue);

主要优势：
- 统一的接口调用方式
- 基于函数指针的灵活架构
- 复用现有的sh367601xb_chip.c功能
- 扩展的管理和状态查询功能
- 完整的示例和文档
*/

#endif /* SH36760_EXAMPLE_H */
